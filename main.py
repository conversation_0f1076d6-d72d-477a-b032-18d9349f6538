import sys, ctypes
import os  # new import for path handling
from PyQt6.QtWidgets import QApplication, QMainWindow, QStackedWidget, QWidget, QVBoxLayout, QPushButton, QLabel, QSplashScreen, QHBoxLayout, QMessageBox
from PyQt6.QtCore import Qt, QThreadPool, QSize
from PyQt6.QtGui import QPixmap, QIcon, QMovie
from PyQt6.QtCore import QTimer
import tempfile
from error_handler import log_error, restart_app

# Import các module chức năng
from data_scrape_ui import MainWindow as DataScrapeWidget
from import_data import ImportScrapedWidget
from internal_data import CreateInternalWidget
from raw_data_ui import RawDataUI  # Thay thế import RawDataWizard
from image_scraping import ImageScrapingWidget  # New import for Image Scraping
from external_update import ExternalUpdateWidget  # New import for External Update
from ai_classification import AIClassificationWidget # Add this import

# Import AI Classification module
import re, time, json, base64, pickle, openai, sqlite3
from pathlib import Path
from collections import deque
from datetime import datetime
from difflib import SequenceMatcher
from PyQt6.QtWidgets import (
    QGroupBox, QLineEdit, QComboBox, QFormLayout, 
    QTextEdit, QTableWidget, QTableWidgetItem, QHeaderView, QProgressBar, QSizePolicy,
    QDialog
)
from PyQt6.QtCore import QObject, QThread, pyqtSignal

# Import module quản lý Google Sheets từ file gsheet_manager.py
from gsheet_manager import GoogleSheetManager

ES_CONTINUOUS = 0x80000000
ES_SYSTEM_REQUIRED = 0x00000001
ES_DISPLAY_REQUIRED = 0x00000002

def prevent_sleep():
    ctypes.windll.kernel32.SetThreadExecutionState(ES_CONTINUOUS | ES_SYSTEM_REQUIRED | ES_DISPLAY_REQUIRED)

def allow_sleep():
    ctypes.windll.kernel32.SetThreadExecutionState(ES_CONTINUOUS)

def resource_path(relative_path):
    # Get absolute path to resource, works for dev and for packaged exe
    try:
        base_path = sys._MEIPASS
    except Exception:
        base_path = os.path.abspath(".")
    return os.path.join(base_path, relative_path)

class AnimatedSplashScreen(QSplashScreen):
    def __init__(self, movie):
        self.movie = movie
        pixmap = QPixmap(self.movie.currentPixmap())
        super().__init__(pixmap)
        self.movie.frameChanged.connect(self.updateFrame)
        # Theo dõi khi frame thay đổi để xử lý loop
        self.movie.frameChanged.connect(self.checkLoop)
        self.movie.start()
        
    def updateFrame(self):
        self.setPixmap(self.movie.currentPixmap())
    
    def checkLoop(self, frame):
        # Nếu frame hiện tại là frame cuối cùng, quay về frame đầu tiên
        if frame == self.movie.frameCount() - 1:
            # Dùng QTimer để trì hoãn việc nhảy về frame đầu tiên
            # tránh tình trạng không hiển thị được frame cuối
            QTimer.singleShot(self.movie.nextFrameDelay(), lambda: self.movie.jumpToFrame(0))
        
    def finish(self, window):
        self.movie.stop()
        super().finish(window)

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowIcon(QIcon(resource_path("UI.ico")))  # Updated: using resource_path
        self.setWindowTitle("Data Assortment All In One")
        self.setGeometry(100, 100, 950, 750)
        self.initUI()
        self.center()  # New: Center the window on screen
        self.about_box_width = 800   # Adjustable width for the About message box
        self.about_box_height = 200  # Adjustable height for the About message box
    
    def initUI(self):
        self.stacked_widget = QStackedWidget()
        self.setCentralWidget(self.stacked_widget)
        
        # Tạo các widget cho từng chức năng
        self.menu_view = self.createMenuView()
        self.raw_data_view = RawDataUI()
        self.autoshopee_scraping_view = DataScrapeWidget()
        self.create_internal_view = CreateInternalWidget()
        self.import_view = ImportScrapedWidget()
        self.image_scraping_view = ImageScrapingWidget()  # New: Image Scraping View
        self.external_update_view = ExternalUpdateWidget()  # New: External Update View
        self.ai_classification_view = AIClassificationWidget(self)  # Add AI Classification widget, set parent
        self.ai_classification_view.back_callback = lambda: self.switchView(self.menu_view)
        self.ai_classification_view.goto_callback = lambda prog: self.goto_program(prog)  # Thêm callback cho chức năng chuyển chương trình
        
        # Setup callback for "Chuyển đến" actions in all modules
        self.autoshopee_scraping_view._goto_program_callback = lambda prog: self.goto_program(prog)
        self.import_view.goto_other_program = lambda prog: self.goto_program(prog)
        self.create_internal_view.goto_other_program = lambda prog: self.goto_program(prog)
        self.raw_data_view._goto_program_callback = lambda prog: self.goto_program(prog)
        self.image_scraping_view.goto_other_program = lambda prog: self.goto_program(prog)  # New: Add callback
        self.external_update_view.goto_other_program = lambda prog: self.goto_program(prog)  # New: Add callback
        self.ai_classification_view.goto_other_program = lambda prog: self.goto_program(prog)  # Add callback
        
        # Thiết lập callback khi nhấn nút Back
        self.raw_data_view.back_callback = lambda: self.switchView(self.menu_view)
        self.autoshopee_scraping_view.back_callback = lambda: self.switchView(self.menu_view)
        self.create_internal_view.back_callback = lambda: self.switchView(self.menu_view)
        self.import_view.back_callback = lambda: self.switchView(self.menu_view)
        self.image_scraping_view.back_callback = lambda: self.switchView(self.menu_view)  # New: Add callback
        self.external_update_view.back_callback = lambda: self.switchView(self.menu_view)  # New: Add callback
        self.ai_classification_view.back_callback = lambda: self.switchView(self.menu_view)  # Add callback
        
        # Thêm các widget vào QStackedWidget
        self.stacked_widget.addWidget(self.menu_view)
        self.stacked_widget.addWidget(self.raw_data_view)
        self.stacked_widget.addWidget(self.autoshopee_scraping_view)
        self.stacked_widget.addWidget(self.create_internal_view)
        self.stacked_widget.addWidget(self.import_view)
        self.stacked_widget.addWidget(self.image_scraping_view)  # New: Add widget to stack
        self.stacked_widget.addWidget(self.external_update_view)  # New: Add widget to stack
        self.stacked_widget.addWidget(self.ai_classification_view)  # Add widget to stack
        
        self.stacked_widget.setCurrentWidget(self.menu_view)
    
    def goto_program(self, program_name: str):
        """
        Map the program name from context menu to the corresponding widget.
        The mapping is:
          - "autoshopee_scraping" --> AutoShopeeScrapingWidget
          - "import_data"         --> ImportScrapedWidget
          - "internal_data"       --> CreateInternalWidget
          - "data_handler"        --> AggregatorWidget
          - "image_scraping"      --> ImageScrapingWidget
          - "external_update"     --> ExternalUpdateWidget
          - "ai_classification"   --> AIClassificationWidget
        """
        mapping = {
            "autoshopee_scraping": self.autoshopee_scraping_view,
            "import_data": self.import_view,
            "internal_data": self.create_internal_view,
            "data_handler": self.raw_data_view,
            "image_scraping": self.image_scraping_view,  # New: Add to mapping
            "external_update": self.external_update_view,  # New: Add to mapping
            "ai_classification": self.ai_classification_view,  # Add to mapping
        }
        target = mapping.get(program_name)
        if target:
            # Kiểm tra xem đang ở AIClassificationWidget và đang chuyển đến một chương trình khác
            current = self.stacked_widget.currentWidget()
            if current == self.ai_classification_view and target != self.ai_classification_view:
                # Sử dụng phương thức goto_callback để ngăn đệ quy vô hạn
                if hasattr(self.ai_classification_view, 'goto_callback'):
                    # Thay vì gọi goto_other_program, trực tiếp chuyển view
                    # và hiển thị thông báo nếu cần
                    if self.ai_classification_view.is_processing():
                        QMessageBox.information(
                            self, 'Thông báo', 
                            'Tiến trình xử lý đang chạy trong nền và sẽ tiếp tục cho đến khi hoàn thành.'
                        )
                    # Sau đó chuyển view
                    self.switchView(target)
                    return
            
            # Các trường hợp khác, chuyển view bình thường
            self.switchView(target)
        else:
            QMessageBox.warning(self, "Warning", f"Không xác định được chương trình: {program_name}")
    
    def switchView(self, widget: QWidget):
        self.stacked_widget.setCurrentWidget(widget)
    
    def createMenuView(self):
        widget = QWidget()
        main_layout = QVBoxLayout(widget)
        main_layout.setContentsMargins(5, 5, 5, 5)  # Reduced top margin
        
        # New: Add About button with hover effect (semi-transparent overlay)
        about_btn = QPushButton("About")
        about_btn.setFixedSize(80, 30)
        about_btn.setStyleSheet("""
            QPushButton {
                border: none;
                background: transparent;
            }
            QPushButton:hover {
                background-color: rgba(0, 0, 0, 0.1);
            }
        """)
        about_btn.clicked.connect(self.show_about)
        main_layout.addWidget(about_btn, alignment=Qt.AlignmentFlag.AlignTop | Qt.AlignmentFlag.AlignLeft)
        
        main_layout.addStretch()  # Push remaining content to center
        
        # Center area: Functional buttons centered in the UI
        center_layout = QVBoxLayout()
        center_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        label = QLabel("Select a program:")
        label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        center_layout.addWidget(label)

        btn_scraping = QPushButton("Data Handler")
        btn_scraping.setFixedSize(300, 50)
        btn_scraping.clicked.connect(lambda: self.switchView(self.raw_data_view))
        center_layout.addWidget(btn_scraping)

        btn_scraping = QPushButton("Data Scraping")
        btn_scraping.setFixedSize(300, 50)
        btn_scraping.clicked.connect(lambda: self.switchView(self.autoshopee_scraping_view))
        center_layout.addWidget(btn_scraping)

        btn_internal = QPushButton("Create Internal Data")
        btn_internal.setFixedSize(300, 50)
        btn_internal.clicked.connect(lambda: self.switchView(self.create_internal_view))
        center_layout.addWidget(btn_internal)
        
        btn_import = QPushButton("Import Data")
        btn_import.setFixedSize(300, 50)
        btn_import.clicked.connect(lambda: self.switchView(self.import_view))
        center_layout.addWidget(btn_import)
        
        # New: Add Image Scraping button
        btn_images = QPushButton("Image Scraping")
        btn_images.setFixedSize(300, 50)
        btn_images.clicked.connect(lambda: self.switchView(self.image_scraping_view))
        center_layout.addWidget(btn_images)

        # New: Add External Update button
        btn_external = QPushButton("Update Level Model")
        btn_external.setFixedSize(300, 50)
        btn_external.clicked.connect(lambda: self.switchView(self.external_update_view))
        center_layout.addWidget(btn_external)
        
        # Add AI Classification button before Exit button
        btn_ai = QPushButton("AI Classification")
        btn_ai.setFixedSize(300, 50)
        btn_ai.clicked.connect(lambda: self.switchView(self.ai_classification_view))
        center_layout.addWidget(btn_ai)

        btn_exit = QPushButton("Exit")
        btn_exit.setFixedSize(300, 50)
        btn_exit.clicked.connect(self.close)
        center_layout.addWidget(btn_exit)
        
        main_layout.addLayout(center_layout)
        main_layout.addStretch()
        return widget

    def show_about(self):
        about_box = QMessageBox(self)
        about_box.setWindowTitle("Thông tin")
        about_box.setText("Product Name: Data Assortment All In One\nDeveloped By: Kiến Quốc\nVersion: 1.21\nCopyright © 2025")
        about_box.setStandardButtons(QMessageBox.StandardButton.Ok)
        # Use the adjustable sizes
        about_box.setFixedSize(self.about_box_width, self.about_box_height)
        about_box.exec()

    def center(self):
        # New: Center the window on the primary screen
        qr = self.frameGeometry()
        cp = QApplication.primaryScreen().availableGeometry().center()
        qr.moveCenter(cp)
        self.move(qr.topLeft())

    def keyPressEvent(self, event):
        if event.key() == Qt.Key.Key_Home:  # New: Show About on Home key press
            self.show_about()
        elif event.key() == Qt.Key.Key_1:
            self.switchView(self.raw_data_view)
        elif event.key() == Qt.Key.Key_2:
            self.switchView(self.autoshopee_scraping_view)
        elif event.key() == Qt.Key.Key_3:
            self.switchView(self.create_internal_view)
        elif event.key() == Qt.Key.Key_4:
            self.switchView(self.import_view)
        elif event.key() == Qt.Key.Key_5:
            self.switchView(self.image_scraping_view)
        elif event.key() == Qt.Key.Key_6:
            self.switchView(self.external_update_view)  # New: Add shortcut
        elif event.key() == Qt.Key.Key_7:
            self.switchView(self.ai_classification_view)  # Add shortcut
        elif event.key() == Qt.Key.Key_8:
            self.close()
        else:
            super().keyPressEvent(event)

    def cleanup(self):
        """Cleanup all running modules"""
        print("Đang dừng tất cả các module...")
        
        # Dừng tất cả các QTimer đang chạy
        for widget in self.findChildren(QWidget):
            for timer in widget.findChildren(QTimer):
                if timer.isActive():
                    print(f"Dừng timer: {timer}")
                    timer.stop()
        
        # Cleanup AutoShopee scraping
        if hasattr(self.autoshopee_scraping_view, 'stop_scraping'):
            try:
                self.autoshopee_scraping_view.stop_scraping()
                print("Đã dừng AutoShopee scraping")
            except Exception as e:
                print(f"Lỗi khi dừng AutoShopee scraping: {e}")
        
        # Cleanup Raw data processing
        if hasattr(self.raw_data_view, 'stop_processing'):
            try:
                self.raw_data_view.stop_processing()
                print("Đã dừng Raw data processing")
            except Exception as e:
                print(f"Lỗi khi dừng Raw data processing: {e}")
            
        # Cleanup Import module
        if hasattr(self.import_view, 'stop_import'):
            try:
                self.import_view.stop_import()
                print("Đã dừng Import module")
            except Exception as e:
                print(f"Lỗi khi dừng Import module: {e}")
            
        # Cleanup Internal data creation
        if hasattr(self.create_internal_view, 'stop_creation'):
            try:
                self.create_internal_view.stop_creation()
                print("Đã dừng Internal data creation")
            except Exception as e:
                print(f"Lỗi khi dừng Internal data creation: {e}")
            
        # Cleanup Image scraping
        if hasattr(self.image_scraping_view, 'stop_image_scraping'):
            try:
                self.image_scraping_view.stop_image_scraping()
                print("Đã dừng Image scraping")
            except Exception as e:
                print(f"Lỗi khi dừng Image scraping: {e}")
        
        # Add cleanup for AI Classification
        if hasattr(self.ai_classification_view, 'cleanup'):
            try:
                self.ai_classification_view.cleanup()
                print("Đã dừng AI Classification")
            except Exception as e:
                print(f"Lỗi khi dừng AI Classification: {e}")
                
        # Đảm bảo rằng QThreadPool đã hoàn thành tất cả các thread
        try:
            thread_pool = QThreadPool.globalInstance()
            print(f"Đang đợi {thread_pool.activeThreadCount()} thread hoàn thành...")
            if thread_pool.activeThreadCount() > 0:
                if not thread_pool.waitForDone(10000):  # Đợi tối đa 10 giây
                    print("CẢNH BÁO: Một số thread không kết thúc đúng cách!")
                else:
                    print("Tất cả thread đã hoàn thành")
        except Exception as e:
            print(f"Lỗi khi đợi thread pool: {e}")
            
        # Làm sạch danh sách signals toàn cục
        try:
            from thread_pool_fix import cleanup_signals
            cleanup_signals()
            print("Đã dọn dẹp signals")
        except Exception as e:
            print(f"Lỗi khi dọn dẹp signals: {e}")
            
        # Đảm bảo tất cả tiến trình đã dừng
        print("Cleanup hoàn tất")

    def closeEvent(self, event):
        """Handle application closing"""
        # Kiểm tra xem các tiến trình quan trọng đang chạy không
        has_active_processes = False
        message = "Bạn có chắc muốn thoát không?"
        
        thread_pool = QThreadPool.globalInstance()
        active_threads = thread_pool.activeThreadCount()
        
        if active_threads > 0:
            has_active_processes = True
            message = f"Đang có {active_threads} tiến trình đang chạy. Thoát bây giờ có thể gây mất dữ liệu.\n\nBạn có chắc muốn thoát không?"
        
        icon = QMessageBox.Icon.Question
        if has_active_processes:
            icon = QMessageBox.Icon.Warning
            
        reply = QMessageBox.question(
            self, 'Xác nhận thoát',
            message,
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            # Kiểm tra lại một lần nữa nếu có tiến trình đang chạy
            if has_active_processes:
                confirm = QMessageBox.warning(
                    self, 'Xác nhận lại',
                    "Bạn THỰC SỰ muốn thoát khi các tiến trình đang chạy?\n(Có thể gây mất dữ liệu)",
                    QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                    QMessageBox.StandardButton.No
                )
                if confirm != QMessageBox.StandardButton.Yes:
                    event.ignore()
                    return
                    
            # Cleanup temp files
            temp_dir = tempfile.gettempdir()
            temp_files = [
                "raw_processed_shop_ids.txt",
                "raw_processed_product_ids.txt", 
                "raw_processed_dest.txt",
                "temp_product_codes.txt",
                "temp_dest_data.txt",
                "temp_spreadsheet_id.txt"
            ]
            
            for fname in temp_files:
                fpath = os.path.join(temp_dir, fname)
                if os.path.exists(fpath):
                    try:
                        os.remove(fpath)
                    except:
                        pass
            
            # Cleanup modules
            self.cleanup()
            
            # Force terminate any remaining threads (last resort)
            thread_pool = QThreadPool.globalInstance()
            if thread_pool.activeThreadCount() > 0:
                print(f"Buộc dừng {thread_pool.activeThreadCount()} thread còn lại...")
                # Không có cách trực tiếp để buộc dừng QThreadPool, 
                # nhưng đảm bảo không thread nào còn lại trước khi thoát
            
            # Đảm bảo thoát hoàn toàn
            print("Đóng ứng dụng...")
            event.accept()
            
            # Đảm bảo tất cả thread đã hoàn thành trước khi thoát
            QTimer.singleShot(500, lambda: self._force_exit())
        else:
            event.ignore()
            
    def _force_exit(self):
        """Buộc thoát ứng dụng nếu vẫn còn thread đang chạy"""
        import os, sys
        print("Buộc thoát ứng dụng")
        os._exit(0)  # Thoát ngay lập tức không cần dọn dẹp thêm

if __name__ == '__main__':
    app = QApplication(sys.argv)
    prevent_sleep()  # Prevent the system from sleeping while the app is running
    
    # Thay thế splash tĩnh bằng splash GIF
    gif_path = resource_path("Splash.gif")
    if os.path.exists(gif_path):
        movie = QMovie(gif_path)
        movie.setScaledSize(QSize(240, 240))
        # Đặt để GIF chạy vô hạn - trong PyQt6 dùng .setCacheMode thay vì setLoopCount
        movie.setCacheMode(QMovie.CacheMode.CacheAll)
        splash = AnimatedSplashScreen(movie)
    else:
        # Fallback to static image if gif not found
        pixmap = QPixmap(resource_path("Splash.jpg"))
        if pixmap.isNull():
            print("Warning: Splash image not found at", resource_path("Splash.jpg"))
            splash = QSplashScreen(QPixmap())  # Empty splash
        else:
            splash_pix = pixmap.scaled(
                200, 200, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation)
            splash = QSplashScreen(splash_pix)
    
    splash.show()
    app.processEvents()  # Ensure splash is rendered

    # Proceed to create the main window after a short delay (e.g., 1500ms)
    def start_main():
        window = MainWindow()
        window.show()
        QTimer.singleShot(100, lambda: splash.finish(window))  # Use finish method to properly stop the movie
        
    QTimer.singleShot(1500, start_main)
    try:
        sys.exit(app.exec())
    except Exception as e:
        log_error(e)
        msg = QMessageBox()
        msg.setWindowTitle("Lỗi")
        msg.setText("Chương trình gặp lỗi không mong muốn. Bạn có muốn khởi động lại không?")
        msg.setStandardButtons(QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
        if msg.exec() == QMessageBox.StandardButton.Yes:
            restart_app()
        else:
            sys.exit(1)
    finally:
        allow_sleep()  # Allow the system to sleep after the app exits
