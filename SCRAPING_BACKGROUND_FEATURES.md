# Tính năng Background Scraping và Confirmation Dialog

## Tổng quan
Đã bổ sung cơ chế cho phép chương trình tiếp tục scrape dữ liệu khi người dùng chuyển đến chương trình <PERSON>, và có hộp thoại xác nhận khi trở về giao diện chính.

## C<PERSON>c thành phần mới được thêm

### 1. Enum và Constants
- **ScrapingState**: Enum quản lý trạng thái scraping (IDLE, RUNNING, PAUSED, STOPPING, COMPLETED, ERROR)
- **ScrapingMessages**: Class chứa các message templates
- **ScrapingConfig**: Class chứa các cấu hình timeout

### 2. ScrapingStateManager Class
**Chức năng chính:**
- Quản lý trạng thái scraping toàn cục
- <PERSON> dõ<PERSON> các thread đang chạy (worker_thread, csv_thread)
- <PERSON><PERSON> cấp thông tin về tiến độ và thời gian chạy
- Dừng threads một cách an toàn

**Methods quan trọng:**
- `is_running()`: Kiểm tra có tiến trình đang chạy
- `is_active()`: Kiểm tra có thread nào active
- `start_scraping()`: Bắt đầu quá trình scraping
- `update_progress()`: Cập nhật tiến độ
- `stop_all_threads()`: Dừng tất cả threads an toàn
- `get_duration()`: Lấy thời gian đã chạy
- `get_progress_text()`: Lấy text hiển thị tiến độ
- `set_current_step()`: Cập nhật bước hiện tại (0=step1, 1=step2)
- `get_current_step()`: Lấy bước hiện tại
- `should_restore_to_step2()`: Kiểm tra có nên restore về step2

### 3. ConfirmationDialog Class
**Chức năng:**
- Dialog xác nhận khi người dùng muốn trở về giao diện chính
- Hiển thị thông tin chi tiết về tiến trình đang chạy
- Cho phép người dùng chọn tiếp tục hoặc dừng

**UI Components:**
- Tiêu đề cảnh báo với icon
- Thông tin chi tiết tiến trình (tên, trạng thái, thời gian, tiến độ)
- 2 nút: "Tiếp tục chạy" và "Dừng tiến trình"

### 4. Cập nhật MainWindow Class

#### Thay đổi trong `__init__()`:
- Thêm `self.scraping_state = ScrapingStateManager()`

#### Thay đổi trong `handle_back()`:
- Kiểm tra có tiến trình active không
- Hiển thị ConfirmationDialog nếu có
- Xử lý lựa chọn của người dùng (tiếp tục/dừng)

#### Thay đổi trong `goto_other_program()`:
- Kiểm tra có tiến trình active không
- Hiển thị thông báo background processing

#### Methods mới:
- `stop_scraping_safely()`: Dừng threads an toàn với ScrapingStateManager
- `restore_ui_state()`: Restore giao diện về đúng bước với đầy đủ UI state
- `reconnect_worker_signals()`: Reconnect signals để tiếp tục nhận updates
- `on_module_activated()`: Được gọi khi module được activate từ main.py
- `update_*_with_persistence()`: Update UI với persistence vào state manager
- Cập nhật `stop_scraping()`: Thêm reset state manager

#### Thay đổi trong `start_processing()`:
- Tích hợp với ScrapingStateManager
- Kết nối signals để cập nhật progress

#### Thay đổi trong `processing_finished()`:
- Cập nhật state manager khi hoàn thành
- Auto reset sau 1 giây

### 5. Cập nhật ThreadSafeCSVWriter (data_scrape_core.py)

#### Methods mới:
- `is_running()`: Kiểm tra writer threads có đang chạy
- `force_shutdown()`: Force shutdown threads ngay lập tức

## Luồng hoạt động

### Khi bắt đầu scraping:
1. ScrapingStateManager được cập nhật với thông tin scraping
2. State chuyển thành RUNNING
3. Progress được theo dõi liên tục

### Khi chuyển đến chương trình khác:
1. Kiểm tra `scraping_state.is_active()`
2. Nếu có tiến trình đang chạy → hiển thị thông báo background continue
3. Chuyển đến chương trình khác mà không dừng scraping

### Khi trở về giao diện chính:
1. Kiểm tra `scraping_state.is_active()`
2. Nếu có tiến trình đang chạy → hiển thị ConfirmationDialog
3. Người dùng chọn:
   - **Tiếp tục**: Không làm gì, quay về giao diện chính
   - **Dừng**: Gọi `stop_scraping_safely()` để dừng an toàn

### Khi hoàn thành scraping:
1. State chuyển thành COMPLETED hoặc ERROR
2. Hiển thị thông báo kết quả
3. Auto reset state manager sau 1 giây

### Khi trở lại module Data Scraping:
1. `main.py` gọi `on_module_activated()` qua `switchView()`
2. `restore_ui_state()` được gọi để kiểm tra trạng thái
3. Nếu `should_restore_to_step2()` = True:
   - Chuyển về step2 (`stacked_widget.setCurrentIndex(1)`)
   - **Restore đầy đủ UI state realtime:**
     - Shop table với trạng thái hiện tại của từng shop
     - Progress bar với % hoàn thành chính xác
     - Total products count đến thời điểm hiện tại
     - Log messages (100 messages gần nhất)
     - Timer với thời gian chạy thực tế
   - **Reconnect worker signals** để tiếp tục nhận updates
   - Hiển thị thông báo chi tiết về tiến trình
4. Nếu không có tiến trình active → giữ nguyên step1

## Tính năng an toàn

### Graceful Shutdown:
- Sử dụng timeout để tránh hang
- Terminate threads nếu không shutdown trong thời gian quy định
- Cleanup resources (files, timers)

### Thread Safety:
- Sử dụng locks trong ThreadSafeCSVWriter
- Event-driven communication giữa threads
- Poison pills để shutdown writer threads

### Error Handling:
- Try-catch blocks cho tất cả operations
- Fallback mechanisms
- User feedback cho mọi trường hợp

## Testing
Đã tạo `test_scraping_state.py` để test các tính năng:
- Mock scraping process
- State transitions
- Confirmation dialog
- Progress tracking

## Realtime UI State Restoration

### UI State Persistence:
- **Shop Status Data**: Lưu trạng thái và product count của từng shop
- **Log Messages**: Lưu 100 messages gần nhất để restore
- **Progress Data**: Lưu completed/total shops và total products
- **Timer State**: Lưu start_time để tính thời gian chạy chính xác

### Signal Reconnection:
- **Automatic Reconnect**: Khi restore về step2, tự động reconnect worker signals
- **Persistence Methods**: Sử dụng `update_*_with_persistence()` để lưu state realtime
- **Duplicate Prevention**: Disconnect existing connections trước khi reconnect

### Memory Management:
- **Log Limit**: Giới hạn 100 log messages để tránh memory leak
- **State Cleanup**: Auto cleanup khi hoàn thành hoặc error
- **Efficient Updates**: Chỉ update state khi có thay đổi thực sự

## Kết luận
Tất cả tính năng đã được implement clean và không hard-code. Hệ thống có thể:
- Tiếp tục scraping khi chuyển chương trình
- Xác nhận an toàn khi trở về giao diện chính
- **Restore đầy đủ UI state realtime** khi trở lại module
- **Hiển thị thông tin chính xác** đến thời điểm hiện tại
- Quản lý state và threads một cách robust
- Cung cấp feedback rõ ràng cho người dùng
