import time
import requests
import pandas as pd
import os
import csv
import datetime
import concurrent.futures
import threading
import re
import random
import queue
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import <PERSON><PERSON><PERSON><PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from openpyxl import Workbook
from openpyxl.utils.dataframe import dataframe_to_rows
from openpyxl.styles import Font
from openpyxl.cell.cell import ILLEGAL_CHARACTERS_RE

# Pool User-Agent để chống fingerprinting
USER_AGENT_POOL = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36"
]

# Pool Accept-Language để variation
ACCEPT_LANGUAGE_POOL = [
    "vi,en-US;q=0.9,en;q=0.8",
    "vi-VN,vi;q=0.9,en;q=0.8",
    "vi,en;q=0.9",
    "vi-VN,vi;q=0.9,en-US;q=0.8,en;q=0.7"
]

# ==========================================
# ANTI-BOT STRATEGY 4 - RETRY ENHANCED
# ==========================================
# 1. 8 luồng đồng thời - tối ưu hiệu suất
# 2. Random delays: 2-5s giữa request, 3-6s giữa shop
# 3. User-Agent rotation với tần suất thấp (30% chance)
# 4. Headers variation ổn định với fallback
# 5. Session refresh mỗi 100 shop để reset fingerprint
# 6. Enhanced retry: 3^n backoff (3,9,27s) + jitter
# 7. Proper empty response handling (không dừng ngay)
# 8. STREAMING EXCEL WRITE - ghi ngay khi có data
# 9. SHOP 0-PRODUCT RETRY - retry 2 lần với delay 15-25s
# ==========================================

# Biến lưu trữ thông tin đăng nhập để tái sử dụng
_auth_lock = threading.Lock()
_auth_info = {
    'cookies': None,
    'headers': None,
    'expiry_time': None
}

def get_logged_in_cookies(username, password, headless=True, force_refresh=False):
    """
    Lấy cookies đăng nhập, sử dụng cache để tránh đăng nhập lại nhiều lần

    Args:
        username: Tên đăng nhập
        password: Mật khẩu
        headless: Chạy ở chế độ headless
        force_refresh: Buộc làm mới thông tin đăng nhập bỏ qua cache

    Returns:
        dict: Cookie đăng nhập dạng dict
    """
    with _auth_lock:
        current_time = time.time()
        # Kiểm tra cache có hết hạn không (hết hạn sau 1 giờ)
        if not force_refresh and _auth_info['cookies'] and _auth_info['expiry_time'] and current_time < _auth_info['expiry_time']:
            return _auth_info['cookies']

        options = Options()
        if headless:
            options.add_argument("--headless=new")
        options.add_argument("--start-maximized")
        options.add_argument("--disable-extensions")
        options.add_argument("--disable-gpu")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--no-sandbox")
        driver = webdriver.Chrome(options=options)

        try:
            driver.get("https://autoshopee.com/login")

            # Sử dụng WebDriverWait thay vì time.sleep cố định
            WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "#login-username"))
            )

            driver.find_element(By.CSS_SELECTOR, "#login-username").send_keys(username)
            driver.find_element(By.CSS_SELECTOR, "#login-password").send_keys(password)
            driver.find_element(By.CSS_SELECTOR, "form button[type='submit']").click()

            # Đợi redirect đến trang dashboard
            WebDriverWait(driver, 10).until(
                lambda d: "dashboard" in d.current_url
            )

            if "dashboard" not in driver.current_url:
                driver.quit()
                raise Exception("❌ Login thất bại, kiểm tra lại thông tin đăng nhập")

            cookies = driver.get_cookies()

            # Lấy CSRF token và các thông tin xác thực khác
            csrf_token = driver.execute_script("""
                // Thử lấy từ cookie
                let match = document.cookie.match(/_csrf=([^;]+)/);
                if (match) return match[1];

                // Thử lấy từ meta tag
                let meta = document.querySelector('meta[name="csrf-token"]');
                if (meta) return meta.getAttribute('content');

                // Thử lấy từ form input
                let input = document.querySelector('input[name="_csrf"]');
                if (input) return input.value;

                return null;
            """)

            # Lấy cookies từ Selenium
            cookie_dict = {cookie['name']: cookie['value'] for cookie in cookies}
            cookie_string = "; ".join([f"{k}={v}" for k, v in cookie_dict.items()])

            # Lấy token từ nhiều nguồn khác nhau
            token_sources = [
                "return window.localStorage.getItem('_csrf') || window.localStorage.getItem('csrf');",
                "return window.sessionStorage.getItem('_csrf') || window.sessionStorage.getItem('csrf');",
                "return document.querySelector('meta[name=\"csrf-token\"]')?.getAttribute('content');",
                "return document.querySelector('input[name=\"_csrf\"]')?.value;"
            ]

            token = None
            for script in token_sources:
                token = driver.execute_script(script)
                if token:
                    break

            if not token and csrf_token:
                token = csrf_token

            # Tạo headers cho request API với random User-Agent
            selected_ua = random.choice(USER_AGENT_POOL)
            selected_lang = random.choice(ACCEPT_LANGUAGE_POOL)

            headers = {
                "Accept": "application/json, text/plain, */*",
                "Accept-Language": selected_lang,
                "Accept-Encoding": "gzip, deflate, br",
                "Cache-Control": "no-cache",
                "Content-Type": "application/json;charset=UTF-8",
                "Cookie": cookie_string,
                "DNT": "1",
                "Origin": "https://autoshopee.com",
                "Pragma": "no-cache",
                "Priority": "u=1, i",
                "Referer": "https://autoshopee.com/core/shopee/product/search",
                "Sec-Ch-Ua": '"Chromium";v="120", "Google Chrome";v="120", ".Not/A)Brand";v="99"',
                "Sec-Ch-Ua-Mobile": "?0",
                "Sec-Ch-Ua-Platform": '"Windows"',
                "Sec-Fetch-Dest": "empty",
                "Sec-Fetch-Mode": "cors",
                "Sec-Fetch-Site": "same-origin",
                "Upgrade-Insecure-Requests": "1",
                "User-Agent": selected_ua,
                "X-Csrf-Token": token or cookie_dict.get('_csrf', ''),
                "X-Requested-With": "XMLHttpRequest"
            }

            # Lưu vào cache với thời gian hết hạn là 1 giờ
            _auth_info['cookies'] = cookie_dict
            _auth_info['headers'] = headers
            _auth_info['expiry_time'] = current_time + 3600  # 1 giờ

            return cookie_dict
        finally:
            driver.quit()

def get_auth_headers():
    """Lấy headers xác thực đã lưu trong cache"""
    with _auth_lock:
        return _auth_info['headers'] if _auth_info['headers'] else None

def create_varied_headers(base_headers):
    """
    Tạo headers với variation nhỏ để tránh fingerprinting

    Args:
        base_headers: Headers gốc từ authentication

    Returns:
        dict: Headers đã được variation
    """
    if not base_headers:
        return None

    # Copy headers gốc
    varied_headers = base_headers.copy()

    # Chỉ thay đổi User-Agent với tần suất thấp hơn để ổn định
    if random.random() < 0.3:  # 30% chance thay đổi UA
        varied_headers["User-Agent"] = random.choice(USER_AGENT_POOL)

    # Chỉ thay đổi Accept-Language với tần suất thấp hơn
    if random.random() < 0.2:  # 20% chance thay đổi Accept-Language
        varied_headers["Accept-Language"] = random.choice(ACCEPT_LANGUAGE_POOL)

    return varied_headers

def fetch_shop_products(match_id, session, headers, timeout=30, max_retries=3, log_callback=None):
    """
    Lấy dữ liệu sản phẩm cho một shop cụ thể với cơ chế retry cho shop 0 sản phẩm

    Args:
        match_id: ID của shop cần lấy dữ liệu
        session: Session requests để tái sử dụng kết nối
        headers: Headers xác thực
        timeout: Thời gian timeout
        max_retries: Số lần thử lại tối đa cho API calls
        log_callback: Callback để ghi log

    Returns:
        list: Danh sách sản phẩm của shop
    """
    def log(message):
        if log_callback:
            log_callback(message)
        else:
            print(message)

    # Cơ chế retry cho shop có 0 sản phẩm
    max_shop_retries = 2  # Retry tối đa 2 lần cho shop 0 sản phẩm
    shop_retry_count = 0

    while shop_retry_count <= max_shop_retries:
        if shop_retry_count == 0:
            log(f"\n🏪 Đang lấy dữ liệu từ shop ID: {match_id}")
        else:
            log(f"\n🔄 Retry lần {shop_retry_count} cho shop ID: {match_id}")

        # Thực hiện lấy dữ liệu shop
        shop_products = _fetch_single_shop_attempt(match_id, session, headers, timeout, max_retries, log_callback)

        # Nếu có sản phẩm hoặc đã retry đủ số lần
        if len(shop_products) > 0 or shop_retry_count >= max_shop_retries:
            break

        # Nếu 0 sản phẩm và chưa retry đủ
        shop_retry_count += 1
        if shop_retry_count <= max_shop_retries:
            retry_delay = random.uniform(15, 25)  # Delay lớn hơn cho shop retry
            log(f"⏳ Shop trả về 0 sản phẩm. Đợi {retry_delay:.1f}s trước khi retry...")
            time.sleep(retry_delay)

    # Log kết quả cuối cùng (chỉ log này mới được UI đếm)
    if len(shop_products) > 0:
        log(f"✅ Shop ID {match_id}: Hoàn thành với {len(shop_products)} sản phẩm")
    else:
        if shop_retry_count > 0:
            log(f"ℹ️ Shop ID {match_id}: Không có sản phẩm nào (đã retry {shop_retry_count} lần)")
        else:
            log(f"ℹ️ Shop ID {match_id}: Không có sản phẩm nào")

    return shop_products

def _fetch_single_shop_attempt(match_id, session, headers, timeout=30, max_retries=3, log_callback=None):
    """
    Thực hiện một lần thử lấy dữ liệu từ shop (helper function)

    Returns:
        list: Danh sách sản phẩm của shop trong lần thử này
    """
    def log(message):
        if log_callback:
            log_callback(message)
        else:
            print(message)

    url = "https://autoshopee.com/core/shopee/product/search/get"

    # API Autoshopee giới hạn mỗi request chỉ trả về tối đa 40 sản phẩm
    page_limit = 40
    page_from = 0
    shop_products = []

    # Theo dõi số lần không có sản phẩm mới để tránh vòng lặp vô hạn
    empty_responses = 0
    max_empty_responses = 3  # Dừng sau 3 lần không có sản phẩm mới
    max_pages = 1000  # Giới hạn số trang tối đa để tránh vòng lặp vô hạn
    current_page = 0

    while empty_responses < max_empty_responses and current_page < max_pages:
        current_page += 1
        payload = {
            "match_id": str(match_id),
            "page_type": "shop",
            "querySoldOut": True,
            "order": "desc",
            "sort_by": "ctime",
            "pageFrom": page_from,
            "pageTo": page_from + page_limit - 1,
            "pageLimit": page_limit
        }

        # Thêm cơ chế retry
        retry_count = 0
        success = False
        data = None

        while retry_count < max_retries and not success:
            try:
                # Tạo headers với variation cho mỗi request (fallback về headers gốc nếu lỗi)
                try:
                    varied_headers = create_varied_headers(headers)
                except:
                    varied_headers = headers  # Fallback về headers gốc

                # Gửi request với timeout và varied headers
                resp = session.post(url, headers=varied_headers, json=payload, timeout=timeout)

                if resp.status_code == 200:
                    try:
                        # Kiểm tra content trước khi parse JSON
                        response_text = resp.text.strip()
                        if not response_text:
                            # Response rỗng có thể là lỗi tạm thời, không phải tín hiệu kết thúc
                            log(f"⚠️ Response rỗng (lần thử {retry_count + 1}/{max_retries}) - có thể là rate limiting")
                        elif not response_text.startswith('{') and not response_text.startswith('['):
                            log(f"❌ Response không phải JSON (lần thử {retry_count + 1}/{max_retries}): {response_text[:100]}...")
                        else:
                            data = resp.json()
                            if data.get("success", False):
                                success = True
                            else:
                                log(f"❌ API lỗi (lần thử {retry_count + 1}/{max_retries}): {data}")
                    except Exception as e:
                        log(f"❌ Lỗi giải mã JSON (lần thử {retry_count + 1}/{max_retries}): {e}")
                        log(f"📄 Response content: {resp.text[:200]}...")
                else:
                    log(f"❌ HTTP lỗi (lần thử {retry_count + 1}/{max_retries}): {resp.status_code}")
                    log(f"📄 Response content: {resp.text[:200]}...")

            except requests.exceptions.Timeout:
                log(f"⏱️ Timeout khi gọi API (lần thử {retry_count + 1}/{max_retries})")
            except requests.exceptions.ConnectionError:
                log(f"🔌 Lỗi kết nối khi gọi API (lần thử {retry_count + 1}/{max_retries})")
            except Exception as e:
                log(f"❌ Lỗi không xác định khi gọi API (lần thử {retry_count + 1}/{max_retries}): {e}")

            if not success:
                retry_count += 1
                if retry_count < max_retries:
                    # Exponential backoff với jitter để tránh thundering herd
                    base_wait = 3 ** retry_count  # 3, 9, 27 giây... (tăng từ 2)
                    jitter = random.uniform(0, 2)  # Thêm random 0-2 giây (tăng từ 1)
                    wait_time = base_wait + jitter
                    log(f"⏳ Đợi {wait_time:.1f} giây trước khi thử lại...")
                    time.sleep(wait_time)

        # Nếu đã retry đủ số lần mà vẫn thất bại
        if not success:
            log("❌ Đã thử lại tối đa số lần nhưng vẫn thất bại. Dừng việc lấy dữ liệu.")
            break

        # Xử lý dữ liệu khi thành công
        products = data.get("data", [])
        product_count = len(products)

        # Kiểm tra nếu có dữ liệu trả về
        if product_count > 0:
            log(f"📦 Trang {current_page}: Sản phẩm từ {page_from} đến {page_from+product_count-1}: {product_count} sản phẩm")
            shop_products.extend(products)
            # Reset bộ đếm khi có sản phẩm
            empty_responses = 0
        else:
            empty_responses += 1
            log(f"ℹ️ Trang {current_page}: Không có sản phẩm ({empty_responses}/{max_empty_responses})")

            # Chỉ thoát khi đã thử đủ số lần liên tiếp không có sản phẩm
            if empty_responses >= max_empty_responses:
                # Không log kết quả ở đây nữa - để main function xử lý
                break

        # Tiến đến trang kế tiếp - chắc chắn tăng theo số lượng 40
        page_from += page_limit

        # Smart delay giữa các request để tránh rate limiting
        request_delay = random.uniform(2.0, 5.0)  # 2-5 giây random - tăng để tránh rate limiting
        log(f"⏳ Đợi {request_delay:.1f}s trước request tiếp theo...")
        time.sleep(request_delay)

    # Trả về kết quả mà không log (để main function quyết định)
    return shop_products

def fetch_all_products_from_multiple_shops_streaming(username, password, match_ids, output_filename, headless=True, timeout=30, max_retries=3, log_callback=None, max_workers=8):
    """
    Lấy dữ liệu từ nhiều shop và ghi Excel streaming (tối ưu hiệu suất)

    Args:
        username: Tên đăng nhập
        password: Mật khẩu
        match_ids: Danh sách ID shop
        output_filename: Tên file Excel đầu ra
        headless: Chạy ở chế độ headless
        timeout: Thời gian timeout
        max_retries: Số lần thử lại tối đa
        log_callback: Callback để ghi log
        max_workers: Số luồng tối đa chạy song song

    Returns:
        bool: True nếu thành công
    """
    def log(message):
        if log_callback:
            log_callback(message)
        else:
            print(message)

    try:
        # Đăng nhập trước để lấy cookies và headers
        log("🔑 Đang đăng nhập vào Autoshopee...")
        get_logged_in_cookies(username, password, headless=headless)

        # Lấy headers đã được chuẩn bị
        headers = get_auth_headers()

        if not headers:
            raise Exception("❌ Không thể lấy thông tin xác thực!")

        log("✅ Đăng nhập thành công.")

        # Khởi tạo Excel writer để ghi streaming
        log("📁 Khởi tạo file Excel...")
        excel_writer = StreamingExcelWriter(output_filename, log_callback)

        # Tạo và cấu hình session requests để tái sử dụng
        session = requests.Session()
        shop_count = 0
        total_products = 0

        # Sử dụng ThreadPoolExecutor để chạy đa luồng
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Bắt đầu các tác vụ lấy dữ liệu cho từng shop và lưu futures
            futures = {
                executor.submit(
                    fetch_shop_products,
                    match_id,
                    session,
                    headers,
                    timeout,
                    max_retries,
                    log_callback
                ): match_id for match_id in match_ids
            }

            # Thu thập kết quả khi các tác vụ hoàn thành và ghi ngay vào Excel
            for i, future in enumerate(concurrent.futures.as_completed(futures)):
                match_id = futures[future]
                try:
                    shop_products = future.result()
                    if shop_products:
                        # Ghi ngay dữ liệu shop vào Excel (không chờ tất cả shop)
                        excel_writer.append_products(shop_products)
                        total_products += len(shop_products)
                        log(f"📊 Đã ghi {len(shop_products)} sản phẩm từ shop {match_id} vào Excel")

                    shop_count += 1
                    log(f"🔄 Tiến độ: {shop_count}/{len(match_ids)} shop đã hoàn thành")

                    # Session refresh mỗi 100 shop để reset fingerprint
                    if shop_count % 100 == 0:
                        log(f"🔄 Refresh session sau {shop_count} shop để tránh detection...")
                        session.close()
                        session = requests.Session()

                        # Thêm delay lớn hơn sau khi refresh session
                        session_refresh_delay = random.uniform(8, 15)
                        log(f"⏳ Đợi {session_refresh_delay:.1f}s sau khi refresh session...")
                        time.sleep(session_refresh_delay)

                    # Delay giữa các shop để tránh quá aggressive
                    elif i < len(match_ids) - 1:  # Không delay ở shop cuối cùng
                        shop_delay = random.uniform(8, 15)  # 8-15 giây
                        log(f"⏳ Đợi {shop_delay:.1f}s trước khi xử lý shop tiếp theo...")
                        time.sleep(shop_delay)

                except Exception as e:
                    log(f"❌ Lỗi khi xử lý shop ID {match_id}: {str(e)}")

        # Hoàn tất và đóng file Excel
        excel_writer.finalize()
        log(f"✅ Hoàn thành! Đã lưu {total_products} sản phẩm vào {output_filename}")
        return True

    except Exception as e:
        log(f"❌ Lỗi: {str(e)}")
        return False

def fetch_all_products_from_multiple_shops(username, password, match_ids, headless=True, timeout=30, max_retries=3, log_callback=None, max_workers=8):
    """
    Lấy dữ liệu từ nhiều shop sử dụng đa luồng (phương pháp cũ - để backward compatibility)

    Args:
        username: Tên đăng nhập
        password: Mật khẩu
        match_ids: Danh sách ID shop
        headless: Chạy ở chế độ headless
        timeout: Thời gian timeout
        max_retries: Số lần thử lại tối đa
        log_callback: Callback để ghi log
        max_workers: Số luồng tối đa chạy song song

    Returns:
        list: Danh sách tất cả sản phẩm từ các shop
    """
    def log(message):
        if log_callback:
            log_callback(message)
        else:
            print(message)

    # Danh sách để lưu tất cả sản phẩm từ các shop
    all_shops_products = []

    try:
        # Đăng nhập trước để lấy cookies và headers
        log("🔑 Đang đăng nhập vào Autoshopee...")
        get_logged_in_cookies(username, password, headless=headless)

        # Lấy headers đã được chuẩn bị
        headers = get_auth_headers()

        if not headers:
            raise Exception("❌ Không thể lấy thông tin xác thực!")

        log("✅ Đăng nhập thành công.")

        # Tạo và cấu hình session requests để tái sử dụng
        session = requests.Session()
        shop_count = 0

        # Sử dụng ThreadPoolExecutor để chạy đa luồng với 3 workers
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Bắt đầu các tác vụ lấy dữ liệu cho từng shop và lưu futures
            futures = {
                executor.submit(
                    fetch_shop_products,
                    match_id,
                    session,
                    headers,
                    timeout,
                    max_retries,
                    log_callback
                ): match_id for match_id in match_ids
            }

            # Thu thập kết quả khi các tác vụ hoàn thành
            for i, future in enumerate(concurrent.futures.as_completed(futures)):
                match_id = futures[future]
                try:
                    shop_products = future.result()
                    all_shops_products.extend(shop_products)
                    shop_count += 1
                    log(f"🔄 Tiến độ: {i+1}/{len(match_ids)} shop đã hoàn thành")

                    # Session refresh mỗi 100 shop để reset fingerprint (tăng từ 75 để ổn định hơn)
                    if shop_count % 100 == 0:
                        log(f"🔄 Refresh session sau {shop_count} shop để tránh detection...")
                        session.close()
                        session = requests.Session()

                        # Thêm delay lớn hơn sau khi refresh session
                        session_refresh_delay = random.uniform(8, 15)
                        log(f"⏳ Đợi {session_refresh_delay:.1f}s sau khi refresh session...")
                        time.sleep(session_refresh_delay)

                    # Delay giữa các shop để tránh quá aggressive
                    elif i < len(match_ids) - 1:  # Không delay ở shop cuối cùng
                        shop_delay = random.uniform(8, 15)  # 8-15 giây
                        log(f"⏳ Đợi {shop_delay:.1f}s trước khi xử lý shop tiếp theo...")
                        time.sleep(shop_delay)

                except Exception as e:
                    log(f"❌ Lỗi khi xử lý shop ID {match_id}: {str(e)}")

    except Exception as e:
        log(f"❌ Lỗi: {str(e)}")

    return all_shops_products

class StreamingExcelWriter:
    """
    Class để ghi Excel streaming - ghi dần dữ liệu thay vì chờ tất cả
    """
    def __init__(self, filename, log_callback=None):
        self.filename = filename
        self.log_callback = log_callback
        self.wb = Workbook()
        self.ws = self.wb.active
        self.current_row = 1
        self.headers_written = False

    def log(self, message):
        if self.log_callback:
            self.log_callback(message)
        else:
            print(message)

    def append_products(self, products):
        """Thêm danh sách sản phẩm vào Excel"""
        if not products:
            return

        try:
            # Làm sạch dữ liệu
            clean_products = []
            for product in products:
                clean_product = {}
                for key, value in product.items():
                    if isinstance(value, str):
                        clean_product[key] = clean_text_for_excel(value)
                    else:
                        clean_product[key] = value
                clean_products.append(clean_product)

            # Tạo DataFrame
            df = pd.json_normalize(clean_products)

            # Tạo DataFrame với cấu trúc cột theo yêu cầu
            new_df = self._format_dataframe(df)

            # Ghi headers nếu chưa ghi
            if not self.headers_written:
                self._write_headers(new_df)
                self.headers_written = True

            # Ghi dữ liệu
            self._write_data(new_df)

        except Exception as e:
            self.log(f"❌ Lỗi khi ghi dữ liệu vào Excel: {str(e)}")

    def _format_dataframe(self, df):
        """Format DataFrame theo yêu cầu"""
        # Tạo dữ liệu cho 3 cột đầu tiên
        key_col = df['id'].astype(str) + '_' + df['shopID'].astype(str)
        sku_col = df['id']
        id_col = df['id']

        # Tạo DataFrame với cấu trúc mới
        new_df = pd.DataFrame({
            '': key_col,  # Cột trống 1
            ' ': sku_col,  # Cột trống 2 (dùng space để phân biệt)
            '  ': id_col   # Cột trống 3 (dùng 2 spaces)
        })

        # Thêm các cột còn lại
        new_df['Link sản phẩm'] = df['linkProduct'] if 'linkProduct' in df.columns else ''
        new_df['Link Shop'] = df['linkShop'] if 'linkShop' in df.columns else ''
        new_df['Tên sản phẩm'] = df['name'] if 'name' in df.columns else ''
        new_df['Thương hiệu'] = df['brand'] if 'brand' in df.columns else ''
        new_df['Mô tả'] = df['description'] if 'description' in df.columns else ''
        new_df['Ngày tạo'] = df['timeCreate'] if 'timeCreate' in df.columns else ''
        new_df['Mã Shop'] = df['itemID'] if 'itemID' in df.columns else ''
        new_df['Mã Sản phẩm'] = df['shopID'] if 'shopID' in df.columns else ''
        new_df['Chuyên mục'] = df['categoryMain'] if 'categoryMain' in df.columns else ''
        new_df['Chuyên mục.1'] = df['categoryTree'] if 'categoryTree' in df.columns else ''
        new_df['Giá hiện tại'] = df['price'] if 'price' in df.columns else ''
        new_df['Giá thấp nhất'] = df['priceMin'] if 'priceMin' in df.columns else ''
        new_df['Giá cao nhất'] = df['priceMax'] if 'priceMax' in df.columns else ''
        new_df['Giảm giá'] = df['discount'] if 'discount' in df.columns else ''
        new_df['Tồn kho'] = df['stock'] if 'stock' in df.columns else ''
        new_df['Cân nặng'] = df['weight'] if 'weight' in df.columns else ''
        new_df['Hình ảnh'] = df['image'] if 'image' in df.columns else ''
        new_df['Số Đánh giá'] = df['cmtCount'] if 'cmtCount' in df.columns else ''
        new_df['Số lượt xem'] = df['viewCount'] if 'viewCount' in df.columns else ''
        new_df['Số thích'] = df['likedCount'] if 'likedCount' in df.columns else ''
        new_df['Điểm đánh giá'] = df['itemRating'] if 'itemRating' in df.columns else ''
        new_df['Đã bán 30 ngày'] = df['sold_30day'] if 'sold_30day' in df.columns else ''
        new_df['Doanh số 30 ngày'] = df['sale_30day'] if 'sale_30day' in df.columns else ''
        new_df['Đã bán toàn thời gian'] = df['sold_alltime'] if 'sold_alltime' in df.columns else ''
        new_df['Doanh số toàn thời gian'] = df['sale_alltime'] if 'sale_alltime' in df.columns else ''
        new_df['Vị trí'] = df['location'] if 'location' in df.columns else ''
        new_df['Video'] = df['video'] if 'video' in df.columns else ''

        return new_df

    def _write_headers(self, df):
        """Ghi headers vào Excel"""
        for c_idx, column in enumerate(df.columns, 1):
            # Đặt header trống cho 3 cột đầu tiên
            if c_idx <= 3:
                header_value = ""
            else:
                header_value = column

            self.ws.cell(row=self.current_row, column=c_idx, value=header_value)
            self.ws.cell(row=self.current_row, column=c_idx).font = Font(bold=False)

        self.current_row += 1

    def _write_data(self, df):
        """Ghi dữ liệu vào Excel"""
        for _, row in df.iterrows():
            for c_idx, value in enumerate(row, 1):
                try:
                    if isinstance(value, str):
                        value = clean_text_for_excel(value)
                    self.ws.cell(row=self.current_row, column=c_idx, value=value)
                except Exception as cell_error:
                    self.log(f"⚠️ Lỗi khi ghi cell ({self.current_row}, {c_idx}): {str(cell_error)}")
                    self.ws.cell(row=self.current_row, column=c_idx, value="[Lỗi dữ liệu]")

            self.current_row += 1

    def finalize(self):
        """Hoàn tất và lưu file Excel"""
        try:
            self.wb.save(self.filename)
            self.log(f"📁 Đã lưu file Excel: {self.filename}")
        except Exception as e:
            self.log(f"❌ Lỗi khi lưu file Excel: {str(e)}")

def clean_text_for_excel(text):
    """
    Làm sạch văn bản để tránh lỗi khi ghi vào Excel mà vẫn giữ nguyên nội dung

    Args:
        text: Văn bản cần làm sạch

    Returns:
        str: Văn bản đã được làm sạch
    """
    if not isinstance(text, str):
        return text

    # Thay thế các ký tự không hợp lệ bằng ký tự tương đương hoặc Unicode
    # Sử dụng regex để tìm và thay thế các ký tự không hợp lệ trong Excel
    return ILLEGAL_CHARACTERS_RE.sub('', text)

class CSVThenExcelWriter:
    """
    Writer class để ghi CSV trước, sau đó convert sang Excel
    Tối ưu tốc độ ghi dữ liệu và fault tolerance
    """

    # Column mapping configuration - không hard code
    COLUMN_MAPPING = {
        # 3 cột đầu tiên có header trống
        'key_column': '',
        'sku_column': ' ',  # Space để phân biệt
        'id_column': '  ',  # 2 spaces để phân biệt

        # Mapping các cột dữ liệu
        'linkProduct': 'Link sản phẩm',
        'linkShop': 'Link Shop',
        'name': 'Tên sản phẩm',
        'brand': 'Thương hiệu',
        'description': 'Mô tả',
        'timeCreate': 'Ngày tạo',
        'itemID': 'Mã Shop',
        'shopID': 'Mã Sản phẩm',
        'categoryMain': 'Chuyên mục',
        'categoryTree': 'Chuyên mục.1',
        'price': 'Giá hiện tại',
        'priceMin': 'Giá thấp nhất',
        'priceMax': 'Giá cao nhất',
        'discount': 'Giảm giá',
        'stock': 'Tồn kho',
        'weight': 'Cân nặng',
        'image': 'Hình ảnh',
        'cmtCount': 'Số Đánh giá',
        'viewCount': 'Số lượt xem',
        'likedCount': 'Số thích',
        'itemRating': 'Điểm đánh giá',
        'sold_30day': 'Đã bán 30 ngày',
        'sale_30day': 'Doanh số 30 ngày',
        'sold_alltime': 'Đã bán toàn thời gian',
        'sale_alltime': 'Doanh số toàn thời gian',
        'location': 'Vị trí',
        'video': 'Video'
    }

    def __init__(self, final_excel_path, log_callback=None):
        """
        Initialize CSV then Excel writer

        Args:
            final_excel_path: Đường dẫn file Excel cuối cùng
            log_callback: Callback function để ghi log
        """
        self.final_excel_path = final_excel_path
        self.log_callback = log_callback

        # Tạo tên file CSV tạm thời
        self.csv_temp_path = self._create_temp_csv_path(final_excel_path)

        # Initialize CSV writer
        self.csv_file = None
        self.csv_writer = None
        self.headers_written = False
        self.total_rows = 0

        # Mở CSV file để ghi
        self._initialize_csv_writer()

    def _create_temp_csv_path(self, excel_path):
        """Tạo đường dẫn file CSV tạm thời"""
        base_name = os.path.splitext(excel_path)[0]
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"{base_name}_temp_{timestamp}.csv"

    def _initialize_csv_writer(self):
        """Khởi tạo CSV writer"""
        try:
            self.csv_file = open(self.csv_temp_path, 'w', newline='', encoding='utf-8')
            self.csv_writer = csv.writer(self.csv_file)
            self.log(f"📁 Khởi tạo CSV temp file: {os.path.basename(self.csv_temp_path)}")
        except Exception as e:
            self.log(f"❌ Lỗi khởi tạo CSV writer: {str(e)}")
            raise

    def log(self, message):
        """Helper function để ghi log"""
        if self.log_callback:
            self.log_callback(message)
        else:
            print(message)

    def append_products(self, products):
        """
        Thêm danh sách sản phẩm vào CSV (siêu nhanh)

        Args:
            products: List các sản phẩm từ API
        """
        if not products:
            return

        try:
            # Làm sạch dữ liệu
            clean_products = self._clean_products_data(products)

            # Ghi headers nếu chưa ghi
            if not self.headers_written:
                self._write_csv_headers(clean_products[0])
                self.headers_written = True

            # Ghi dữ liệu vào CSV (siêu nhanh)
            for product in clean_products:
                row_data = self._format_product_row(product)
                self.csv_writer.writerow(row_data)
                self.total_rows += 1

            # Flush để đảm bảo dữ liệu được ghi ngay
            self.csv_file.flush()

        except Exception as e:
            self.log(f"❌ Lỗi khi ghi CSV: {str(e)}")

    def _clean_products_data(self, products):
        """Làm sạch dữ liệu sản phẩm"""
        clean_products = []
        for product in products:
            clean_product = {}
            for key, value in product.items():
                if isinstance(value, str):
                    clean_product[key] = clean_text_for_excel(value)
                else:
                    clean_product[key] = value
            clean_products.append(clean_product)
        return clean_products

    def _write_csv_headers(self, sample_product):
        """Ghi headers vào CSV dựa trên sample product"""
        # Tạo headers theo thứ tự cố định
        headers = self._get_ordered_headers(sample_product)
        self.csv_writer.writerow(headers)

    def _get_ordered_headers(self, sample_product):
        """Lấy headers theo thứ tự cố định"""
        # Bắt đầu với 3 cột đầu tiên
        headers = ['id', 'shopID', 'id']  # Raw field names cho CSV

        # Thêm các cột khác theo thứ tự trong COLUMN_MAPPING
        for field_name in self.COLUMN_MAPPING.keys():
            if field_name not in ['key_column', 'sku_column', 'id_column']:
                if field_name in sample_product:
                    headers.append(field_name)

        return headers

    def _format_product_row(self, product):
        """Format một row sản phẩm cho CSV"""
        # Tạo key column (id_shopID)
        key_value = f"{product.get('id', '')}__{product.get('shopID', '')}"

        # Bắt đầu với 3 cột đầu tiên
        row = [
            key_value,  # Key column
            product.get('id', ''),  # SKU column
            product.get('id', '')   # ID column
        ]

        # Thêm các cột khác theo thứ tự
        for field_name in self.COLUMN_MAPPING.keys():
            if field_name not in ['key_column', 'sku_column', 'id_column']:
                row.append(product.get(field_name, ''))

        return row

    def finalize(self):
        """
        Hoàn tất quá trình: đóng CSV và convert sang Excel

        Returns:
            bool: True nếu thành công
        """
        try:
            # Đóng CSV file
            if self.csv_file:
                self.csv_file.close()
                self.log(f"📊 Đã ghi {self.total_rows} rows vào CSV")

            # Convert CSV sang Excel với smart retry
            self.log("🔄 Bắt đầu convert CSV → Excel...")
            success = self._convert_csv_to_excel_with_retry()

            if success:
                self.log(f"✅ Hoàn tất! File Excel: {self.final_excel_path}")
                # Xóa file CSV tạm thời
                self._cleanup_temp_files()
                return True
            else:
                self.log(f"❌ Conversion thất bại! CSV backup: {self.csv_temp_path}")
                return False

        except Exception as e:
            self.log(f"❌ Lỗi trong finalize: {str(e)}")
            return False

    def _convert_csv_to_excel_with_retry(self, max_retries=3):
        """Convert CSV sang Excel với smart retry mechanism"""
        for attempt in range(max_retries):
            try:
                self.log(f"🔄 Conversion attempt {attempt + 1}/{max_retries}")

                if attempt == 0:
                    # Method 1: Sử dụng format chuẩn (giống StreamingExcelWriter)
                    success = self._convert_using_standard_format()
                elif attempt == 1:
                    # Method 2: Pandas simple conversion
                    success = self._convert_using_pandas_simple()
                else:
                    # Method 3: Chunked processing cho file lớn
                    success = self._convert_using_chunked_processing()

                if success:
                    return True

            except Exception as e:
                self.log(f"❌ Attempt {attempt + 1} failed: {str(e)}")

        return False

    def _convert_using_standard_format(self):
        """Method 1: Convert với format chuẩn giống StreamingExcelWriter"""
        try:
            # Đọc CSV
            df = pd.read_csv(self.csv_temp_path)

            # Format DataFrame giống hệt StreamingExcelWriter
            formatted_df = self._format_dataframe_like_streaming_writer(df)

            # Tạo Excel với format chuẩn
            wb = Workbook()
            ws = wb.active

            # Ghi headers (3 cột đầu trống)
            for c_idx, column in enumerate(formatted_df.columns, 1):
                if c_idx <= 3:
                    header_value = ""  # 3 cột đầu trống
                else:
                    header_value = column
                ws.cell(row=1, column=c_idx, value=header_value)
                ws.cell(row=1, column=c_idx).font = Font(bold=False)

            # Ghi dữ liệu từ row 2
            for r_idx, (_, row) in enumerate(formatted_df.iterrows(), 2):
                for c_idx, value in enumerate(row, 1):
                    try:
                        if isinstance(value, str):
                            value = clean_text_for_excel(value)
                        ws.cell(row=r_idx, column=c_idx, value=value)
                    except:
                        ws.cell(row=r_idx, column=c_idx, value="[Lỗi dữ liệu]")

            wb.save(self.final_excel_path)
            return True

        except Exception:
            return False

    def _format_dataframe_like_streaming_writer(self, df):
        """Format DataFrame giống hệt StreamingExcelWriter._format_dataframe()"""
        # Tạo dữ liệu cho 3 cột đầu tiên từ CSV
        key_col = df.iloc[:, 0]  # Cột đầu tiên là key
        sku_col = df.iloc[:, 1]  # Cột thứ 2 là sku
        id_col = df.iloc[:, 2]   # Cột thứ 3 là id

        # Tạo DataFrame mới với cấu trúc chuẩn
        new_df = pd.DataFrame({
            '': key_col,    # Cột trống 1
            ' ': sku_col,   # Cột trống 2
            '  ': id_col    # Cột trống 3
        })

        # Thêm các cột khác theo COLUMN_MAPPING
        for field_name, excel_header in self.COLUMN_MAPPING.items():
            if field_name not in ['key_column', 'sku_column', 'id_column']:
                if field_name in df.columns:
                    new_df[excel_header] = df[field_name]
                else:
                    new_df[excel_header] = ''

        return new_df

    def _convert_using_pandas_simple(self):
        """Method 2: Fallback conversion với pandas"""
        try:
            df = pd.read_csv(self.csv_temp_path)
            df.to_excel(self.final_excel_path, index=False)
            return True
        except Exception:
            return False

    def _convert_using_chunked_processing(self):
        """Method 3: Chunked processing cho file lớn"""
        try:
            chunk_size = 5000
            wb = Workbook()
            ws = wb.active
            current_row = 1
            headers_written = False

            for chunk in pd.read_csv(self.csv_temp_path, chunksize=chunk_size):
                if not headers_written:
                    # Ghi headers
                    for c_idx, col in enumerate(chunk.columns, 1):
                        ws.cell(row=current_row, column=c_idx, value=col)
                    current_row += 1
                    headers_written = True

                # Ghi data
                for _, row in chunk.iterrows():
                    for c_idx, value in enumerate(row, 1):
                        ws.cell(row=current_row, column=c_idx, value=value)
                    current_row += 1

            wb.save(self.final_excel_path)
            return True
        except Exception:
            return False

    def _cleanup_temp_files(self):
        """Xóa các file tạm thời"""
        try:
            if os.path.exists(self.csv_temp_path):
                os.remove(self.csv_temp_path)
                self.log(f"🗑️ Đã xóa CSV temp file")
        except Exception as e:
            self.log(f"⚠️ Không thể xóa temp file: {str(e)}")

class ThreadSafeCSVWriter:
    """
    Thread-safe CSV Writer với 4 writer threads xử lý shop-by-shop
    Mỗi writer thread nhận toàn bộ dữ liệu của 1 shop và ghi vào 1 CSV file với lock
    """

    # Column mapping configuration - không hard code
    COLUMN_MAPPING = {
        # 3 cột đầu tiên có header trống
        'key_column': '',
        'sku_column': ' ',  # Space để phân biệt
        'id_column': '  ',  # 2 spaces để phân biệt

        # Mapping các cột dữ liệu
        'linkProduct': 'Link sản phẩm',
        'linkShop': 'Link Shop',
        'name': 'Tên sản phẩm',
        'brand': 'Thương hiệu',
        'description': 'Mô tả',
        'timeCreate': 'Ngày tạo',
        'itemID': 'Mã Shop',
        'shopID': 'Mã Sản phẩm',
        'categoryMain': 'Chuyên mục',
        'categoryTree': 'Chuyên mục.1',
        'price': 'Giá hiện tại',
        'priceMin': 'Giá thấp nhất',
        'priceMax': 'Giá cao nhất',
        'discount': 'Giảm giá',
        'stock': 'Tồn kho',
        'weight': 'Cân nặng',
        'image': 'Hình ảnh',
        'cmtCount': 'Số Đánh giá',
        'viewCount': 'Số lượt xem',
        'likedCount': 'Số thích',
        'itemRating': 'Điểm đánh giá',
        'sold_30day': 'Đã bán 30 ngày',
        'sale_30day': 'Doanh số 30 ngày',
        'sold_alltime': 'Đã bán toàn thời gian',
        'sale_alltime': 'Doanh số toàn thời gian',
        'location': 'Vị trí',
        'video': 'Video'
    }

    def __init__(self, final_excel_path, log_callback=None, num_writers=4):
        """
        Initialize Thread-safe CSV Writer với shop-by-shop processing

        Args:
            final_excel_path: Đường dẫn file Excel cuối cùng
            log_callback: Callback function để ghi log
            num_writers: Số lượng writer threads (mặc định 4)
        """
        self.final_excel_path = final_excel_path
        self.log_callback = log_callback
        self.num_writers = num_writers

        # Tạo tên file CSV tạm thời duy nhất
        self.csv_temp_path = self._create_temp_csv_path(final_excel_path)

        # Queue chứa shop data (mỗi item là toàn bộ dữ liệu của 1 shop)
        # UNLIMITED QUEUE - không giới hạn để Worker Threads không bị block
        # Pool phải chứa toàn bộ dữ liệu để Writer Threads luôn có việc làm
        self.shop_queue = queue.Queue()  # Unlimited pool cho competitive access

        # Control events
        self.shutdown_event = threading.Event()

        # Thread safety locks
        self.file_lock = threading.Lock()  # Lock cho việc ghi file
        self.stats_lock = threading.Lock()
        # Không cần queue_lock vì unlimited queue không bao giờ block

        # Statistics tracking
        self.total_rows = 0
        self.total_shops_processed = 0
        self.headers_written = False

        # Writer threads
        self.writer_threads = []

        # Initialize CSV file
        self.csv_file = None
        self._initialize_csv_file()

        # Start writer threads
        self._start_writer_threads()

    def _create_temp_csv_path(self, excel_path):
        """Tạo đường dẫn file CSV tạm thời"""
        base_name = os.path.splitext(excel_path)[0]
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"{base_name}_temp_{timestamp}.csv"

    def _initialize_csv_file(self):
        """Khởi tạo CSV file"""
        try:
            self.csv_file = open(self.csv_temp_path, 'w', newline='', encoding='utf-8')
            self.log(f"📁 Khởi tạo CSV temp file: {os.path.basename(self.csv_temp_path)}")
        except Exception as e:
            self.log(f"❌ Lỗi khởi tạo CSV file: {str(e)}")
            raise

    def _start_writer_threads(self):
        """Khởi động writer threads để xử lý shop-by-shop"""
        for i in range(self.num_writers):
            thread = threading.Thread(
                target=self._writer_worker,
                args=(i,),  # Pass writer ID
                name=f"CSVWriter-{i+1}",
                daemon=True
            )
            thread.start()
            self.writer_threads.append(thread)
            self.log(f"🚀 Đã khởi động Writer {i+1} (Thread: {thread.name})")

        self.log(f"✅ Hoàn tất khởi động {self.num_writers} CSV writer threads cho competitive shop processing")

    def _writer_worker(self, writer_id):
        """Worker function cho writer threads - thread-safe lấy shop từ pool"""
        self.log(f"🏃 Writer {writer_id+1}: Sẵn sàng lấy shop từ pool (thread-safe)...")

        shops_processed = 0

        while not self.shutdown_event.is_set():
            try:
                # RESPONSIVE POLLING: Lấy shop ngay khi có trong pool
                shop_data = self.shop_queue.get(timeout=0.1)  # Giảm timeout để responsive hơn

                if shop_data is None:  # Poison pill để shutdown
                    self.shop_queue.task_done()
                    break

                shop_id = shop_data.get('shop_id', 'Unknown')
                products = shop_data.get('products', [])

                # Log khi lấy được shop từ pool với pool size sau khi lấy
                remaining_pool_size = self.shop_queue.qsize()
                self.log(f"🎯 Writer {writer_id+1}: Lấy shop {shop_id} ({len(products)} sản phẩm) → Pool còn: {remaining_pool_size}")

                if not products:
                    self.log(f"⚠️ Writer {writer_id+1}: Shop {shop_id} không có dữ liệu")
                    self.shop_queue.task_done()
                    continue

                # Xử lý shop data (chỉ lock khi ghi file)
                start_time = time.time()
                self._process_shop_competitive(writer_id, shop_id, products)
                process_time = time.time() - start_time

                shops_processed += 1

                # Log tóm gọn hơn
                if process_time > 0.5:  # Chỉ log nếu xử lý lâu hơn 500ms
                    self.log(f"⚠️ Writer {writer_id+1}: Shop {shop_id} xử lý chậm {process_time:.2f}s")
                # Không log khi xử lý nhanh để giảm spam

                # Mark task as done và quay lại pool ngay lập tức
                self.shop_queue.task_done()

            except queue.Empty:
                # Pool trống, tiếp tục polling (không log để tránh spam)
                continue
            except Exception as e:
                self.log(f"❌ Writer {writer_id+1} lỗi xử lý: {str(e)}")
                try:
                    self.shop_queue.task_done()
                except:
                    pass

        self.log(f"🏁 Writer {writer_id+1}: Đã xử lý {shops_processed} shops - Thoát pool")

    def _process_shop_competitive(self, writer_id, shop_id, products):
        """Xử lý shop data với competitive approach - chỉ lock khi ghi file"""
        try:
            # BƯỚC 1: Xử lý dữ liệu NGOÀI lock (song song với writers khác)
            clean_products = self._clean_products_data(products)

            if not clean_products:
                return

            # BƯỚC 2: Chuẩn bị tất cả rows NGOÀI lock (song song)
            shop_rows = []
            headers = None

            # Chuẩn bị headers nếu cần
            if not self.headers_written:
                headers = self._get_ordered_headers(clean_products[0])

            # Chuẩn bị tất cả rows
            for product in clean_products:
                row_data = self._format_product_row(product)
                shop_rows.append(row_data)

            # BƯỚC 3: CHỈ LOCK KHI GHI FILE (tối thiểu thời gian lock)
            with self.file_lock:
                csv_writer = csv.writer(self.csv_file)

                # Ghi headers nếu chưa ghi (atomic check and write)
                if headers and not self.headers_written:
                    csv_writer.writerow(headers)
                    self.csv_file.flush()
                    self.headers_written = True
                    self.log(f"📋 Headers được ghi bởi Writer {writer_id+1}")

                # Ghi tất cả rows của shop (atomic write)
                csv_writer.writerows(shop_rows)
                self.csv_file.flush()

                # Update statistics (trong lock để đảm bảo consistency)
                self.total_rows += len(shop_rows)
                self.total_shops_processed += 1

                # Log tóm gọn - chỉ log mỗi 10 shops hoặc shop lớn
                if self.total_shops_processed % 10 == 0 or len(shop_rows) > 50:
                    self.log(f"💾 Writer {writer_id+1}: Ghi shop {shop_id} ({len(shop_rows)} rows) - Total: {self.total_shops_processed} shops")

        except Exception as e:
            self.log(f"❌ Writer {writer_id+1} lỗi xử lý shop {shop_id}: {str(e)}")

    def log(self, message):
        """Helper function để ghi log"""
        if self.log_callback:
            self.log_callback(message)
        else:
            print(message)

    def is_running(self):
        """Kiểm tra xem writer threads có đang chạy không"""
        return any(thread.is_alive() for thread in self.writer_threads)

    def force_shutdown(self, timeout=5.0):
        """Force shutdown tất cả writer threads ngay lập tức"""
        try:
            # Set shutdown event
            self.shutdown_event.set()

            # Send poison pills
            for i in range(self.num_writers):
                try:
                    self.shop_queue.put(None, timeout=0.1)
                except:
                    pass

            # Force join với timeout ngắn
            for thread in self.writer_threads:
                thread.join(timeout=timeout)

            # Đóng file nếu còn mở
            if self.csv_file:
                try:
                    self.csv_file.close()
                except:
                    pass

            self.log(f"🛑 Force shutdown hoàn tất")
            return True

        except Exception as e:
            self.log(f"❌ Lỗi force shutdown: {str(e)}")
            return False

    def append_shop_data(self, shop_id, products):
        """
        Thêm toàn bộ dữ liệu của 1 shop vào pool để writer threads lấy

        Args:
            shop_id: ID của shop
            products: List các sản phẩm từ API của shop này
        """
        if not products:
            self.log(f"⚠️ Shop {shop_id}: Không có dữ liệu sản phẩm")
            return

        try:
            # Tạo shop data package
            shop_data = {
                'shop_id': shop_id,
                'products': products
            }

            # Log trước khi thêm với pool dynamics
            pre_pool_size = self.shop_queue.qsize()

            # Thread-safe put shop data vào unlimited pool (không bao giờ block)
            self.shop_queue.put(shop_data)  # Unlimited queue - Worker Threads không bị block

            # Log tóm gọn pool dynamics
            current_pool_size = self.shop_queue.qsize()

            # Log mọi pool dynamics như yêu cầu
            self.log(f"📥 Shop {shop_id} ({len(products)} sản phẩm) → Pool: {pre_pool_size}→{current_pool_size}")

            # Debug: Nếu pool size không tăng như mong đợi
            if current_pool_size <= pre_pool_size:
                self.log(f"🚨 DEBUG: Pool size không tăng! Pre: {pre_pool_size}, Current: {current_pool_size}")
            elif current_pool_size > 1:
                self.log(f"🎉 Pool tích lũy: {current_pool_size} shops - Writer Threads có nhiều việc làm!")

        except Exception as e:
            self.log(f"❌ Lỗi khi thêm shop {shop_id} vào pool: {str(e)}")

    # Backward compatibility - keep old method name
    def append_products(self, products, shop_id="Unknown"):
        """
        Backward compatibility method - chuyển đổi sang append_shop_data

        Args:
            products: List các sản phẩm từ API
            shop_id: ID của shop (mặc định "Unknown")
        """
        self.append_shop_data(shop_id, products)

    def _clean_products_data(self, products):
        """Làm sạch dữ liệu sản phẩm"""
        clean_products = []
        for product in products:
            clean_product = {}
            for key, value in product.items():
                if isinstance(value, str):
                    clean_product[key] = clean_text_for_excel(value)
                else:
                    clean_product[key] = value
            clean_products.append(clean_product)
        return clean_products

    def _queue_headers(self, sample_product):
        """Queue headers vào CSV dựa trên sample product"""
        headers = self._get_ordered_headers(sample_product)
        header_item = {
            'type': 'header',
            'data': headers
        }
        self.data_queue.put(header_item)

    def _get_ordered_headers(self, sample_product):
        """Lấy headers theo thứ tự cố định"""
        # Bắt đầu với 3 cột đầu tiên (unique names để tránh duplicate)
        headers = ['key_column', 'sku_column', 'id_column']  # Unique field names cho CSV

        # Thêm các cột khác theo thứ tự trong COLUMN_MAPPING
        for field_name in self.COLUMN_MAPPING.keys():
            if field_name not in ['key_column', 'sku_column', 'id_column']:
                if field_name in sample_product:
                    headers.append(field_name)

        return headers

    def _format_product_row(self, product):
        """Format một row sản phẩm cho CSV"""
        # Tạo key column (id_shopID)
        key_value = f"{product.get('id', '')}__{product.get('shopID', '')}"

        # Bắt đầu với 3 cột đầu tiên
        row = [
            key_value,  # Key column
            product.get('id', ''),  # SKU column
            product.get('id', '')   # ID column
        ]

        # Thêm các cột khác theo thứ tự
        for field_name in self.COLUMN_MAPPING.keys():
            if field_name not in ['key_column', 'sku_column', 'id_column']:
                row.append(product.get(field_name, ''))

        return row

    def finalize(self):
        """
        Hoàn tất quá trình: đợi shop queue xử lý xong, shutdown threads và convert sang Excel

        Returns:
            bool: True nếu thành công
        """
        try:
            # Đợi tất cả shop data trong queue được xử lý
            self.log("⏳ Đang đợi tất cả shop data được xử lý...")
            self.shop_queue.join()
            self.log("✅ Shop queue đã hoàn thành")

            # Shutdown writer threads
            self._shutdown_writer_threads()

            # Đóng CSV file
            if self.csv_file:
                self.csv_file.close()
                self.log(f"📊 Đã ghi {self.total_rows} rows từ {self.total_shops_processed} shops vào CSV")

            # Convert CSV sang Excel với smart retry
            self.log("🔄 Bắt đầu convert CSV → Excel...")
            success = self._convert_csv_to_excel_with_retry()

            if success:
                self.log(f"✅ Hoàn tất! File Excel: {self.final_excel_path}")
                # Xóa file CSV tạm thời
                self._cleanup_temp_files()
                return True
            else:
                self.log(f"❌ Conversion thất bại! CSV backup: {self.csv_temp_path}")
                return False

        except Exception as e:
            self.log(f"❌ Lỗi trong finalize: {str(e)}")
            return False

    def _shutdown_writer_threads(self):
        """Shutdown tất cả writer threads một cách an toàn"""
        # Set shutdown event
        self.shutdown_event.set()

        # Send poison pills đến shop queue
        for i in range(self.num_writers):
            try:
                self.shop_queue.put(None, timeout=1.0)
            except queue.Full:
                self.log(f"⚠️ Shop queue đầy khi gửi poison pill {i+1}")

        # Đợi tất cả threads kết thúc
        for thread in self.writer_threads:
            thread.join(timeout=10.0)  # Timeout 10 giây
            if thread.is_alive():
                self.log(f"⚠️ Thread {thread.name} không shutdown trong thời gian quy định")

        self.log(f"🛑 Đã shutdown {len(self.writer_threads)} writer threads")



    def _convert_csv_to_excel_with_retry(self, max_retries=3):
        """Convert CSV sang Excel với smart retry mechanism"""
        for attempt in range(max_retries):
            try:
                self.log(f"🔄 Conversion attempt {attempt + 1}/{max_retries}")

                if attempt == 0:
                    # Method 1: Sử dụng format chuẩn (giống StreamingExcelWriter)
                    success = self._convert_using_standard_format()
                elif attempt == 1:
                    # Method 2: Pandas simple conversion
                    success = self._convert_using_pandas_simple()
                else:
                    # Method 3: Chunked processing cho file lớn
                    success = self._convert_using_chunked_processing()

                if success:
                    return True

            except Exception as e:
                self.log(f"❌ Attempt {attempt + 1} failed: {str(e)}")

        return False

    def _convert_using_standard_format(self):
        """Method 1: Convert với format chuẩn giống StreamingExcelWriter"""
        try:
            # Đọc CSV
            df = pd.read_csv(self.csv_temp_path)
            self.log(f"🔍 CSV đọc được: {len(df)} rows, {len(df.columns)} columns")
            self.log(f"🔍 CSV columns: {list(df.columns)}")

            # Format DataFrame giống hệt StreamingExcelWriter
            formatted_df = self._format_dataframe_like_streaming_writer(df)
            self.log(f"🔍 Formatted DF: {len(formatted_df)} rows, {len(formatted_df.columns)} columns")

            # Tạo Excel với format chuẩn
            wb = Workbook()
            ws = wb.active

            # Ghi headers (3 cột đầu trống)
            for c_idx, column in enumerate(formatted_df.columns, 1):
                if c_idx <= 3:
                    header_value = ""  # 3 cột đầu trống
                else:
                    header_value = column
                ws.cell(row=1, column=c_idx, value=header_value)
                ws.cell(row=1, column=c_idx).font = Font(bold=False)

            # Ghi dữ liệu từ row 2
            data_rows_written = 0
            for r_idx, (_, row) in enumerate(formatted_df.iterrows(), 2):
                for c_idx, value in enumerate(row, 1):
                    try:
                        if isinstance(value, str):
                            value = clean_text_for_excel(value)
                        ws.cell(row=r_idx, column=c_idx, value=value)
                    except:
                        ws.cell(row=r_idx, column=c_idx, value="[Lỗi dữ liệu]")
                data_rows_written += 1

            self.log(f"🔍 Đã ghi {data_rows_written} data rows vào Excel")
            wb.save(self.final_excel_path)
            return True

        except Exception as e:
            self.log(f"❌ Lỗi conversion method 1: {str(e)}")
            return False

    def _format_dataframe_like_streaming_writer(self, df):
        """Format DataFrame giống hệt StreamingExcelWriter._format_dataframe()"""
        # Tạo dữ liệu cho 3 cột đầu tiên từ CSV với column names mới
        key_col = df['key_column'] if 'key_column' in df.columns else df.iloc[:, 0]
        sku_col = df['sku_column'] if 'sku_column' in df.columns else df.iloc[:, 1]
        id_col = df['id_column'] if 'id_column' in df.columns else df.iloc[:, 2]

        # Tạo DataFrame mới với cấu trúc chuẩn
        new_df = pd.DataFrame({
            '': key_col,    # Cột trống 1
            ' ': sku_col,   # Cột trống 2
            '  ': id_col    # Cột trống 3
        })

        # Thêm các cột khác theo COLUMN_MAPPING
        for field_name, excel_header in self.COLUMN_MAPPING.items():
            if field_name not in ['key_column', 'sku_column', 'id_column']:
                if field_name in df.columns:
                    new_df[excel_header] = df[field_name]
                else:
                    new_df[excel_header] = ''

        return new_df

    def _convert_using_pandas_simple(self):
        """Method 2: Fallback conversion với pandas"""
        try:
            df = pd.read_csv(self.csv_temp_path)
            df.to_excel(self.final_excel_path, index=False)
            return True
        except Exception:
            return False

    def _convert_using_chunked_processing(self):
        """Method 3: Chunked processing cho file lớn"""
        try:
            chunk_size = 5000
            wb = Workbook()
            ws = wb.active
            current_row = 1
            headers_written = False

            for chunk in pd.read_csv(self.csv_temp_path, chunksize=chunk_size):
                if not headers_written:
                    # Ghi headers
                    for c_idx, col in enumerate(chunk.columns, 1):
                        ws.cell(row=current_row, column=c_idx, value=col)
                    current_row += 1
                    headers_written = True

                # Ghi data
                for _, row in chunk.iterrows():
                    for c_idx, value in enumerate(row, 1):
                        ws.cell(row=current_row, column=c_idx, value=value)
                    current_row += 1

            wb.save(self.final_excel_path)
            return True
        except Exception:
            return False

    def _cleanup_temp_files(self):
        """Xóa file CSV tạm thời"""
        try:
            if os.path.exists(self.csv_temp_path):
                os.remove(self.csv_temp_path)
                self.log(f"🗑️ Đã xóa CSV temp file")
        except Exception as e:
            self.log(f"⚠️ Không thể xóa temp file: {str(e)}")

def save_to_excel(products, filename="autoshopee_products.xlsx", add_shop_info=True, log_callback=None):
    """
    Lưu danh sách sản phẩm vào file Excel với định dạng cột theo yêu cầu

    Args:
        products (list): Danh sách các sản phẩm từ API
        filename (str, optional): Tên file Excel đầu ra. Mặc định là "autoshopee_products.xlsx"
        add_shop_info (bool, optional): Thêm thông tin shop vào dữ liệu. Mặc định là True
        log_callback (function): Callback function để ghi log
    """
    def log(message):
        if log_callback:
            log_callback(message)
        else:
            print(message)

    try:
        # Kiểm tra xem products có phải là list không
        if not isinstance(products, list):
            log("❌ Dữ liệu không phải là danh sách sản phẩm hợp lệ")
            return

        if not products:
            log("❌ Không có sản phẩm nào để lưu")
            return

        # Kiểm tra xem sản phẩm đầu tiên có phải là dict không
        if not isinstance(products[0], dict):
            log("❌ Dữ liệu sản phẩm không đúng định dạng")
            return

        # Kiểm tra các trường quan trọng
        important_fields = ['id', 'name', 'price']
        missing_fields = [field for field in important_fields if field not in products[0]]
        if missing_fields:
            log(f"⚠️ Dữ liệu thiếu các trường quan trọng: {', '.join(missing_fields)}")

        # Làm sạch dữ liệu để tránh lỗi khi ghi vào Excel
        clean_products = []
        for product in products:
            clean_product = {}
            for key, value in product.items():
                if isinstance(value, str):
                    clean_product[key] = clean_text_for_excel(value)
                else:
                    clean_product[key] = value
            clean_products.append(clean_product)

        # Tạo DataFrame từ danh sách sản phẩm đã làm sạch
        df = pd.json_normalize(clean_products)

        # Thêm thông tin shop để phân biệt sản phẩm từ các shop khác nhau
        if add_shop_info and 'shopID' in df.columns:
            # Nhóm sản phẩm theo shopID và đếm số lượng
            shop_counts = df.groupby('shopID').size().reset_index(name='product_count')
            log("\n📊 Thống kê theo shop:")
            for _, row in shop_counts.iterrows():
                shop_id = row['shopID']
                product_count = row['product_count']
                log(f"  - Shop ID {shop_id}: {product_count} sản phẩm")

        # 1. Chuyển đổi các cột sang kiểu số nếu cần
        for col in ['stock', 'sold_30day', 'price', 'priceMin', 'priceMax']:
            if col in df.columns and df[col].dtype == 'object':
                df[col] = pd.to_numeric(df[col], errors='coerce')

        # 2. Tạo các cột key và sku nếu chúng không tồn tại
        if 'id' in df.columns:
            # Nếu không có cột key, tạo cột key từ id
            if 'key' not in df.columns:
                df['key'] = df['id'].astype(str) + '_' + df['shopID'].astype(str)  # Thêm shopID làm suffix

            # Nếu không có cột sku, tạo cột sku từ id
            if 'sku' not in df.columns:
                df['sku'] = df['id']

        # Tạo DataFrame mới với cấu trúc cột theo yêu cầu
        # Tạo dữ liệu cho 3 cột đầu tiên
        key_col = df['id'].astype(str) + '_' + df['shopID'].astype(str)
        sku_col = df['id']
        id_col = df['id']

        # Tạo DataFrame với 3 cột đầu tiên có header trống
        new_df = pd.DataFrame({
            'key': key_col,
            'sku': sku_col,
            'id': id_col
        })

        # Đổi tên các cột thành trống
        new_df = new_df.rename(columns={
            'key': '',
            'sku': '',
            'id': ''
        })

        # Thêm các cột còn lại
        new_df['Link sản phẩm'] = df['linkProduct'] if 'linkProduct' in df.columns else ''
        new_df['Link Shop'] = df['linkShop'] if 'linkShop' in df.columns else ''
        new_df['Tên sản phẩm'] = df['name'] if 'name' in df.columns else ''
        new_df['Thương hiệu'] = df['brand'] if 'brand' in df.columns else ''
        new_df['Mô tả'] = df['description'] if 'description' in df.columns else ''
        new_df['Ngày tạo'] = df['timeCreate'] if 'timeCreate' in df.columns else ''
        new_df['Mã Shop'] = df['itemID'] if 'itemID' in df.columns else ''
        new_df['Mã Sản phẩm'] = df['shopID'] if 'shopID' in df.columns else ''
        new_df['Chuyên mục'] = df['categoryMain'] if 'categoryMain' in df.columns else ''
        new_df['Chuyên mục.1'] = df['categoryTree'] if 'categoryTree' in df.columns else ''
        new_df['Giá hiện tại'] = df['price'] if 'price' in df.columns else ''
        new_df['Giá thấp nhất'] = df['priceMin'] if 'priceMin' in df.columns else ''
        new_df['Giá cao nhất'] = df['priceMax'] if 'priceMax' in df.columns else ''
        new_df['Giảm giá'] = df['discount'] if 'discount' in df.columns else ''
        new_df['Tồn kho'] = df['stock'] if 'stock' in df.columns else ''
        new_df['Cân nặng'] = df['weight'] if 'weight' in df.columns else ''
        new_df['Hình ảnh'] = df['image'] if 'image' in df.columns else ''
        new_df['Số Đánh giá'] = df['cmtCount'] if 'cmtCount' in df.columns else ''
        new_df['Số lượt xem'] = df['viewCount'] if 'viewCount' in df.columns else ''
        new_df['Số thích'] = df['likedCount'] if 'likedCount' in df.columns else ''
        new_df['Điểm đánh giá'] = df['itemRating'] if 'itemRating' in df.columns else ''
        new_df['Đã bán 30 ngày'] = df['sold_30day'] if 'sold_30day' in df.columns else ''
        new_df['Doanh số 30 ngày'] = df['sale_30day'] if 'sale_30day' in df.columns else ''
        new_df['Đã bán toàn thời gian'] = df['sold_alltime'] if 'sold_alltime' in df.columns else ''
        new_df['Doanh số toàn thời gian'] = df['sale_alltime'] if 'sale_alltime' in df.columns else ''
        new_df['Vị trí'] = df['location'] if 'location' in df.columns else ''
        new_df['Video'] = df['video'] if 'video' in df.columns else ''

        # Tạo workbook mới
        wb = Workbook()
        ws = wb.active

        # Lỗi thường xảy ra với các ký tự đặc biệt - thêm xử lý an toàn
        try:
            # Thêm dữ liệu từ DataFrame
            rows = dataframe_to_rows(new_df, index=False, header=True)
            for r_idx, row in enumerate(rows, 1):
                for c_idx, value in enumerate(row, 1):
                    try:
                        # Thêm xử lý đặc biệt cho các ký tự không hợp lệ
                        if isinstance(value, str):
                            # Làm sạch giá trị một lần nữa để đảm bảo an toàn
                            value = clean_text_for_excel(value)
                        ws.cell(row=r_idx, column=c_idx, value=value)

                        # Đặt font không in đậm cho header
                        if r_idx == 1:
                            ws.cell(row=r_idx, column=c_idx).font = Font(bold=False)
                    except Exception as cell_error:
                        log(f"⚠️ Lỗi khi thêm giá trị vào ô ({r_idx}, {c_idx}): {str(cell_error)}")
                        # Thay thế bằng giá trị an toàn
                        ws.cell(row=r_idx, column=c_idx, value="[Ký tự không hỗ trợ]")
        except Exception as e:
            log(f"⚠️ Lỗi khi chuyển DataFrame sang Excel: {str(e)}")
            # Thử phương pháp khác để lưu file
            try:
                log("🔄 Thử phương pháp khác để lưu file...")
                # Lưu trực tiếp bằng pandas
                with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                    new_df.to_excel(writer, index=False, sheet_name='Sheet')
                log(f"📁 Đã lưu file Excel: {filename} bằng phương pháp thay thế")
                return True
            except Exception as alt_error:
                log(f"❌ Lỗi khi lưu file bằng phương pháp thay thế: {str(alt_error)}")
                return False

        try:
            # Lưu workbook
            wb.save(filename)
            log(f"📁 Đã lưu file Excel: {filename} (header không in đậm)")
        except Exception as save_error:
            log(f"❌ Lỗi khi lưu file Excel: {str(save_error)}")
            return False

        # In thông tin về số lượng sản phẩm và các trường dữ liệu
        log(f"📊 Thống kê:")
        log(f"  - Số lượng sản phẩm: {len(products)}")
        log(f"  - Số lượng trường dữ liệu: {len(new_df.columns)}")
        log(f"  - Đã sắp xếp các cột theo yêu cầu")

        return True

    except Exception as e:
        log(f"❌ Lỗi không xác định khi lưu file Excel: {str(e)}")
        return False

def fetch_all_products_from_multiple_shops_csv_then_excel(username, password, match_ids, output_filename, headless=True, timeout=30, max_retries=3, log_callback=None, max_workers=8):
    """
    Lấy dữ liệu từ nhiều shop và ghi CSV trước, sau đó convert sang Excel (tối ưu tốc độ)
    Sử dụng CSVThenExcelWriter (phương pháp cũ)

    Args:
        username: Tên đăng nhập
        password: Mật khẩu
        match_ids: Danh sách ID shop
        output_filename: Tên file Excel đầu ra
        headless: Chạy ở chế độ headless
        timeout: Thời gian timeout
        max_retries: Số lần thử lại tối đa
        log_callback: Callback để ghi log
        max_workers: Số luồng tối đa chạy song song

    Returns:
        bool: True nếu thành công
    """
    def log(message):
        if log_callback:
            log_callback(message)
        else:
            print(message)

    try:
        # Đăng nhập trước để lấy cookies và headers
        log("🔑 Đang đăng nhập vào Autoshopee...")
        get_logged_in_cookies(username, password, headless=headless)

        # Lấy headers đã được chuẩn bị
        headers = get_auth_headers()

        if not headers:
            raise Exception("❌ Không thể lấy thông tin xác thực!")

        log("✅ Đăng nhập thành công.")

        # Khởi tạo CSV then Excel writer để ghi tối ưu
        log("📁 Khởi tạo CSV writer...")
        csv_excel_writer = CSVThenExcelWriter(output_filename, log_callback)

        # Tạo và cấu hình session requests để tái sử dụng
        session = requests.Session()
        shop_count = 0
        total_products = 0

        # Sử dụng ThreadPoolExecutor để chạy đa luồng
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Bắt đầu các tác vụ lấy dữ liệu cho từng shop và lưu futures
            futures = {
                executor.submit(
                    fetch_shop_products,
                    match_id,
                    session,
                    headers,
                    timeout,
                    max_retries,
                    log_callback
                ): match_id for match_id in match_ids
            }

            # Thu thập kết quả khi các tác vụ hoàn thành và ghi ngay vào CSV
            for i, future in enumerate(concurrent.futures.as_completed(futures)):
                match_id = futures[future]
                try:
                    shop_products = future.result()
                    if shop_products:
                        # Ghi ngay dữ liệu shop vào CSV (siêu nhanh)
                        csv_excel_writer.append_products(shop_products)
                        total_products += len(shop_products)
                        log(f"📊 Đã ghi {len(shop_products)} sản phẩm từ shop {match_id} vào CSV")

                    shop_count += 1
                    log(f"🔄 Tiến độ: {shop_count}/{len(match_ids)} shop đã hoàn thành")

                    # Session refresh mỗi 100 shop để reset fingerprint
                    if shop_count % 100 == 0:
                        log(f"🔄 Refresh session sau {shop_count} shop để tránh detection...")
                        session.close()
                        session = requests.Session()

                        # Thêm delay lớn hơn sau khi refresh session
                        session_refresh_delay = random.uniform(8, 15)
                        log(f"⏳ Đợi {session_refresh_delay:.1f}s sau khi refresh session...")
                        time.sleep(session_refresh_delay)

                    # Delay giữa các shop để tránh quá aggressive
                    elif i < len(match_ids) - 1:  # Không delay ở shop cuối cùng
                        shop_delay = random.uniform(8, 15)  # 8-15 giây
                        log(f"⏳ Đợi {shop_delay:.1f}s trước khi xử lý shop tiếp theo...")
                        time.sleep(shop_delay)

                except Exception as e:
                    log(f"❌ Lỗi khi xử lý shop ID {match_id}: {str(e)}")

        # Hoàn tất và convert CSV sang Excel
        success = csv_excel_writer.finalize()
        if success:
            log(f"✅ Hoàn thành! Đã lưu {total_products} sản phẩm vào {output_filename}")
        return success

    except Exception as e:
        log(f"❌ Lỗi: {str(e)}")
        return False

def fetch_all_products_from_multiple_shops_threadsafe_csv(username, password, match_ids, output_filename, headless=True, timeout=30, max_retries=3, log_callback=None, max_workers=8, num_writers=4):
    """
    Lấy dữ liệu từ nhiều shop và ghi CSV với 4 thread writer + Queue system (tối ưu tốc độ và thread-safe)

    Args:
        username: Tên đăng nhập
        password: Mật khẩu
        match_ids: Danh sách ID shop
        output_filename: Tên file Excel đầu ra
        headless: Chạy ở chế độ headless
        timeout: Thời gian timeout
        max_retries: Số lần thử lại tối đa
        log_callback: Callback để ghi log
        max_workers: Số luồng tối đa chạy song song cho scraping
        num_writers: Số lượng writer threads cho CSV (mặc định 4)

    Returns:
        bool: True nếu thành công
    """
    def log(message):
        if log_callback:
            log_callback(message)
        else:
            print(message)

    try:
        # Đăng nhập trước để lấy cookies và headers
        log("🔑 Đang đăng nhập vào Autoshopee...")
        get_logged_in_cookies(username, password, headless=headless)

        # Lấy headers đã được chuẩn bị
        headers = get_auth_headers()

        if not headers:
            raise Exception("❌ Không thể lấy thông tin xác thực!")

        log("✅ Đăng nhập thành công.")

        # Khởi tạo ThreadSafe CSV writer với 4 writer threads
        log(f"📁 Khởi tạo ThreadSafe CSV writer với {num_writers} writer threads...")
        threadsafe_writer = ThreadSafeCSVWriter(output_filename, log_callback, num_writers)

        # Tạo và cấu hình session requests để tái sử dụng
        session = requests.Session()
        shop_count = 0
        total_products = 0

        # Sử dụng ThreadPoolExecutor để chạy đa luồng
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Bắt đầu các tác vụ lấy dữ liệu cho từng shop và lưu futures
            futures = {
                executor.submit(
                    fetch_shop_products,
                    match_id,
                    session,
                    headers,
                    timeout,
                    max_retries,
                    log_callback
                ): match_id for match_id in match_ids
            }

            # REAL-TIME STREAMING: Shop hoàn thành → Ngay lập tức vào pool
            for i, future in enumerate(concurrent.futures.as_completed(futures)):
                match_id = futures[future]
                try:
                    shop_products = future.result()
                    if shop_products:
                        # NGAY LẬP TỨC đưa vào pool để Writer Threads lấy (KHÔNG chờ anti-bot delay)
                        threadsafe_writer.append_shop_data(match_id, shop_products)
                        total_products += len(shop_products)
                        # Log shop completion đầy đủ như yêu cầu
                        log(f"⚡ Shop {match_id}: Hoàn thành → Ngay lập tức vào pool ({len(shop_products)} sản phẩm)")

                    shop_count += 1
                    # Log main progress mỗi shop như yêu cầu
                    log(f"🔄 Tiến độ: {shop_count}/{len(match_ids)} shop đã hoàn thành")

                    # ANTI-BOT DELAYS - BẮT BUỘC để tránh detection (KHÔNG ảnh hưởng pool)
                    # Session refresh mỗi 100 shop để reset fingerprint
                    if shop_count % 100 == 0:
                        log(f"🔄 Refresh session sau {shop_count} shop để tránh detection...")
                        session.close()
                        session = requests.Session()

                        # Thêm delay lớn hơn sau khi refresh session
                        session_refresh_delay = random.uniform(8, 15)
                        log(f"⏳ Đợi {session_refresh_delay:.1f}s sau khi refresh session...")
                        time.sleep(session_refresh_delay)

                    # ANTI-BOT: Delay giữa các shop để tránh quá aggressive (KHÔNG ảnh hưởng pool)
                    elif i < len(match_ids) - 1:  # Không delay ở shop cuối cùng
                        shop_delay = random.uniform(3, 6)  # 3-6 giây - ANTI-BOT STRATEGY (giảm từ 8-15s)
                        log(f"⏳ Anti-bot delay: {shop_delay:.1f}s...")
                        time.sleep(shop_delay)

                except Exception as e:
                    log(f"❌ Lỗi khi xử lý shop ID {match_id}: {str(e)}")

            log(f"🏁 Tất cả Worker Threads đã hoàn thành → Pool size cuối: {threadsafe_writer.shop_queue.qsize()}")

        # Hoàn tất và convert CSV sang Excel
        success = threadsafe_writer.finalize()
        if success:
            log(f"✅ Hoàn thành! Đã lưu {total_products} sản phẩm vào {output_filename}")
        return success

    except Exception as e:
        log(f"❌ Lỗi: {str(e)}")
        return False

def fetch_and_save_multiple_shops(username, password, match_ids, output_filename=None, headless=True, timeout=30, max_retries=3, log_callback=None, max_workers=8, use_threadsafe_writer=True):
    """
    Lấy dữ liệu từ nhiều shop và lưu vào cùng một file Excel

    Args:
        username: Tên đăng nhập
        password: Mật khẩu
        match_ids: Danh sách ID shop
        output_filename: Tên file Excel đầu ra
        headless: Chạy ở chế độ headless
        timeout: Thời gian timeout
        max_retries: Số lần thử lại tối đa
        log_callback: Callback để ghi log
        max_workers: Số luồng tối đa chạy song song
        use_threadsafe_writer: Sử dụng ThreadSafeCSVWriter (mặc định True) hoặc CSVThenExcelWriter (False)

    Returns:
        str: Tên file Excel đã lưu
    """
    def log(message):
        if log_callback:
            log_callback(message)
        else:
            print(message)

    if output_filename is None:
        # Tạo tên file với timestamp để tránh xung đột
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        output_filename = f"autoshopee_products_{timestamp}.xlsx"

    # Chọn writer method dựa trên tham số
    if use_threadsafe_writer:
        # Sử dụng ThreadSafe CSV Writer với 4 writer threads (mới)
        log("🚀 Sử dụng ThreadSafe CSV Writer với 4 writer threads")
        success = fetch_all_products_from_multiple_shops_threadsafe_csv(
            username,
            password,
            match_ids,
            output_filename,
            headless=headless,
            timeout=timeout,
            max_retries=max_retries,
            log_callback=log_callback,
            max_workers=max_workers,
            num_writers=4
        )
    else:
        # Sử dụng CSV then Excel để tối ưu tốc độ ghi (cũ)
        log("📁 Sử dụng CSV then Excel Writer (phương pháp cũ)")
        success = fetch_all_products_from_multiple_shops_csv_then_excel(
            username,
            password,
            match_ids,
            output_filename,
            headless=headless,
            timeout=timeout,
            max_retries=max_retries,
            log_callback=log_callback,
            max_workers=max_workers
        )

    if success:
        return output_filename
    else:
        log("❌ Xử lý dữ liệu thất bại")
        return None
