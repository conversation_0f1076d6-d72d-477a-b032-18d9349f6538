import os
import time
import re
import unicodedata
from gsheet_manager import GoogleSheetManager

# Thông tin OAuth2 được mã hóa Base64
OAUTH2_CREDENTIALS = "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"

# Thông tin về template
TEMPLATE_SPREADSHEET_ID = "1zrpyulMDZGcB7wAH9NXrRMdjOLYw4h3Tsf8n7EvS5oU"
TEMPLATE_SHEET_TITLE = "Pool Deal"
DEFAULT_TARGET_SHEET = "Pool Deal"

class RawDataCore:
    def __init__(self):
        # Khởi tạo kết nối đến Google Sheet bằng OAuth2
        self.sheet_manager = GoogleSheetManager(auth_type='oauth', credentials_data=OAUTH2_CREDENTIALS)
        # Khởi tạo các service cần thiết
        self.sheets_service = self.sheet_manager.get_sheets_service()
        self.drive_service = self.sheet_manager.get_drive_service()

    def normalize_number_format(self, value):
        """
        <PERSON>ẩn hóa định dạng số để xử lý vấn đề locale (dấu chấm vs dấu phẩy)

        Với valueInputOption='USER_ENTERED', Google Sheets sẽ tự động nhận diện:
        - Số hợp lệ → Hiển thị như số (không có dấu nháy đơn)
        - Text → Hiển thị như text

        Xử lý các trường hợp:
        - 219.000 → 219000 (loại bỏ dấu chấm phân cách để thành số thuần)
        - 219,000.40 → 219000.40 (loại bỏ dấu phẩy phân cách, giữ thập phân)
        - 219.000,40 → 219000.40 (chuyển EU format thành số thuần)
        - 123.45 → 123.45 (số thập phân đơn giản)

        Args:
            value: Giá trị cần chuẩn hóa

        Returns:
            Giá trị số thuần không có ký tự phân cách (để Google Sheets nhận diện là số)
        """
        if not value or not isinstance(value, str):
            return value

        # Loại bỏ khoảng trắng
        value = value.strip()

        # Trường hợp 1: Format EU - 219.000,40 (chấm phân cách nghìn, phẩy thập phân)
        if re.match(r'^\d{1,3}(\.\d{3})+,\d{1,2}$', value):
            # Chuyển đổi: 219.000,40 → 219000.40 (số thuần)
            parts = value.split(',')
            integer_part = parts[0].replace('.', '')  # 219.000 → 219000
            decimal_part = parts[1]  # 40
            normalized = f"{integer_part}.{decimal_part}"
            print(f"Chuẩn hóa số EU format: '{value}' -> '{normalized}' (số thuần)")
            return normalized

        # Trường hợp 2: Format US - 219,000.40 (phẩy phân cách nghìn, chấm thập phân)
        elif re.match(r'^\d{1,3}(,\d{3})+\.\d{1,2}$', value):
            # Chuyển đổi: 219,000.40 → 219000.40 (số thuần)
            normalized = value.replace(',', '')
            print(f"Chuẩn hóa số US format: '{value}' -> '{normalized}' (số thuần)")
            return normalized

        # Trường hợp 3: Chỉ có dấu chấm phân cách hàng nghìn - 219.000
        elif re.match(r'^\d{1,3}(\.\d{3})+$', value):
            # Chuyển thành số thuần: 219.000 → 219000
            normalized = value.replace('.', '')
            print(f"Chuẩn hóa số nguyên: '{value}' -> '{normalized}' (số thuần)")
            return normalized

        # Trường hợp 4: Chỉ có dấu phẩy phân cách hàng nghìn - 219,000
        elif re.match(r'^\d{1,3}(,\d{3})+$', value):
            # Chuyển thành số thuần: 219,000 → 219000
            normalized = value.replace(',', '')
            print(f"Chuẩn hóa số nguyên: '{value}' -> '{normalized}' (số thuần)")
            return normalized

        # Trường hợp 5: Số thập phân đơn giản - 123.45
        elif re.match(r'^\d+\.\d{1,2}$', value):
            # Giữ nguyên (đã là số thuần)
            return value

        # Trường hợp 6: Số thập phân với dấu phẩy - 123,45 (format EU)
        elif re.match(r'^\d+,\d{1,2}$', value):
            # Chuyển thành dấu chấm: 123,45 → 123.45 (số thuần)
            normalized = value.replace(',', '.')
            print(f"Chuẩn hóa số thập phân EU: '{value}' -> '{normalized}' (số thuần)")
            return normalized

        # Trường hợp 7: Số nguyên đơn giản - 123
        elif re.match(r'^\d+$', value):
            # Giữ nguyên (đã là số thuần)
            return value

        # Các trường hợp khác giữ nguyên (text)
        return value

    def _process_percentage_format(self, value):
        """
        Xử lý định dạng phần trăm

        Args:
            value: Giá trị cần xử lý

        Returns:
            Giá trị đã được format phần trăm
        """
        if not value or not isinstance(value, str):
            return value

        # Loại bỏ khoảng trắng
        value = value.strip()

        # Nếu đã có dấu %, giữ nguyên
        if value.endswith('%'):
            return value

        # Nếu là số, thêm dấu %
        try:
            # Thử convert sang float để kiểm tra có phải số không
            float_val = float(value.replace(',', '.'))
            return f"{value}%"
        except ValueError:
            # Không phải số, giữ nguyên
            return value

    def remove_vietnamese_accents(self, text):
        """
        Chuyển đổi tiếng Việt có dấu thành không dấu và xóa khoảng trắng

        Args:
            text: Chuỗi tiếng Việt cần chuyển đổi

        Returns:
            Chuỗi không dấu, không khoảng trắng, lowercase
        """
        if not text:
            return ""

        # Chuyển về lowercase
        text = text.lower()

        # Loại bỏ dấu tiếng Việt
        text = unicodedata.normalize('NFD', text)
        text = ''.join(char for char in text if unicodedata.category(char) != 'Mn')

        # Xử lý các ký tự đặc biệt tiếng Việt không được normalize
        vietnamese_chars = {
            'đ': 'd', 'Đ': 'd',
            'ă': 'a', 'Ă': 'a',
            'â': 'a', 'Â': 'a',
            'ê': 'e', 'Ê': 'e',
            'ô': 'o', 'Ô': 'o',
            'ơ': 'o', 'Ơ': 'o',
            'ư': 'u', 'Ư': 'u'
        }

        for vn_char, latin_char in vietnamese_chars.items():
            text = text.replace(vn_char, latin_char)

        # Xóa tất cả khoảng trắng và ký tự đặc biệt
        text = re.sub(r'[^a-z0-9]', '', text)

        return text

    def is_validation_column(self, header):
        """
        Kiểm tra xem header có phải là cột validation cần dùng RAW không

        Args:
            header: Tên header cần kiểm tra

        Returns:
            True nếu là cột validation, False nếu không
        """
        # Danh sách các cột validation cần dùng RAW (chuẩn hóa bằng hàm remove_vietnamese_accents)
        validation_headers_original = [
            "Cho phép Shopee, Beyond-K, Diệp Lê và các KOL tham gia phiên Livestream được quyền công bố giá sau cùng*trên các nền tảng mạng xã hội cho mục đích truyền thông về phiên Livestream?",
            "Cho phép Shopee, Beyond-K, Diệp Lê và các KOL tham gia phiên Livestream được sử dụng hình ảnh/thông tin sản phẩm vào trong teaser video/teaser post cho mục đích truyền thông về phiên Livestream ?"
        ]

        # Chuẩn hóa các header validation bằng cùng hàm
        validation_columns_normalized = [self.remove_vietnamese_accents(h) for h in validation_headers_original]

        # Chuyển header về dạng không dấu để so sánh
        header_normalized = self.remove_vietnamese_accents(header)

        # Kiểm tra khớp chính xác với các cột validation
        for validation_col in validation_columns_normalized:
            if header_normalized == validation_col:
                print(f"Phát hiện cột validation: {header[:50]}... → Sẽ dùng RAW mode")
                return True

        return False

    def get_sheet_list(self, spreadsheet_id):
        """
        Lấy danh sách các sheet trong một spreadsheet

        Args:
            spreadsheet_id: ID của spreadsheet cần lấy danh sách sheet

        Returns:
            Danh sách tên các sheet (đã lọc bỏ các sheet không cần thiết)
        """
        try:
            spreadsheet = self.sheet_manager.open_by_key(spreadsheet_id)
            worksheets = spreadsheet.worksheets()

            # Danh sách các sheet cần loại bỏ
            excluded_sheets = [
                "Template input deal (Vietnamese)",
                "Hướng dẫn sử dụng file",
                "Deal nominate"
            ]

            # Lọc và chỉ giữ lại các sheet không nằm trong danh sách loại trừ
            filtered_sheets = [sheet.title for sheet in worksheets if sheet.title not in excluded_sheets]

            return filtered_sheets
        except Exception as e:
            print(f"Lỗi khi lấy danh sách sheet: {str(e)}")
            return []

    def get_sheet_headers(self, spreadsheet_id, sheet_name, header_row=2):
        """
        Lấy danh sách header từ sheet

        Args:
            spreadsheet_id: ID của spreadsheet
            sheet_name: Tên sheet cần lấy header
            header_row: Dòng chứa header (mặc định là dòng 2)

        Returns:
            Danh sách các header
        """
        try:
            # Mở spreadsheet và sheet
            spreadsheet = self.sheet_manager.open_by_key(spreadsheet_id)
            sheet = spreadsheet.worksheet(sheet_name)

            # Lấy dòng header từ sheet
            range_name = f"{sheet_name}!A{header_row}:ZZ{header_row}"  # Lấy từ cột A đến ZZ cho dòng header
            result = self.sheets_service.spreadsheets().values().get(
                spreadsheetId=spreadsheet_id,
                range=range_name
            ).execute()

            # Lấy các giá trị trong dòng header
            values = result.get('values', [])

            if not values or not values[0]:
                print(f"Không tìm thấy header trong sheet '{sheet_name}' tại dòng {header_row}")
                return []

            # Trả về danh sách các header
            return values[0]
        except Exception as e:
            print(f"Lỗi khi lấy header từ sheet '{sheet_name}': {str(e)}")
            return []

    def create_sheet_from_template(self, target_spreadsheet_id, target_sheet_title=DEFAULT_TARGET_SHEET):
        """
        Tạo sheet đích từ sheet template

        Args:
            target_spreadsheet_id: ID của spreadsheet để tạo sheet đích
            target_sheet_title: Tên sheet đích sẽ tạo (mặc định là "Pool Deal")

        Returns:
            Sheet ID của sheet đã tạo
        """
        try:
            # Mở spreadsheet đích
            target_spreadsheet = self.sheet_manager.open_by_key(target_spreadsheet_id)

            # Kiểm tra xem sheet đích đã tồn tại chưa
            try:
                existing_sheet = target_spreadsheet.worksheet(target_sheet_title)
                # Nếu đã tồn tại, xóa sheet cũ
                target_spreadsheet.del_worksheet(existing_sheet)
                print(f"Đã xóa sheet cũ '{target_sheet_title}'")
            except:
                # Sheet chưa tồn tại, không cần xóa
                pass

            # Mở spreadsheet template
            template_spreadsheet = self.sheet_manager.open_by_key(TEMPLATE_SPREADSHEET_ID)
            template_sheet = template_spreadsheet.worksheet(TEMPLATE_SHEET_TITLE)

            # Sao chép sheet template sang spreadsheet đích
            copy_sheet_request = {
                "destinationSpreadsheetId": target_spreadsheet_id
            }

            copy_result = self.sheets_service.spreadsheets().sheets().copyTo(
                spreadsheetId=TEMPLATE_SPREADSHEET_ID,
                sheetId=template_sheet.id,
                body=copy_sheet_request
            ).execute()

            # Lấy ID của sheet mới được tạo
            new_sheet_id = copy_result.get('sheetId')

            # Đổi tên sheet (xóa "Copy of" và đặt tên mới)
            worksheet_list = target_spreadsheet.worksheets()
            for sheet in worksheet_list:
                if sheet.title.startswith(f"Copy of {TEMPLATE_SHEET_TITLE}"):
                    # Đổi tên sheet
                    request = {
                        "requests": [
                            {
                                "updateSheetProperties": {
                                    "properties": {
                                        "sheetId": sheet.id,
                                        "title": target_sheet_title
                                    },
                                    "fields": "title"
                                }
                            }
                        ]
                    }

                    self.sheets_service.spreadsheets().batchUpdate(
                        spreadsheetId=target_spreadsheet_id,
                        body=request
                    ).execute()

                    print(f"Đã tạo sheet '{target_sheet_title}' từ template thành công")
                    return sheet.id

            raise Exception("Không thể tìm thấy sheet vừa tạo")

        except Exception as e:
            print(f"Lỗi khi tạo sheet từ template: {str(e)}")
            return None

    def column_to_index(self, column_name):
        """
        Chuyển đổi tên cột (A, B, C, ..., AA, AB, ...) sang chỉ số (0, 1, 2, ...)

        Args:
            column_name: Tên cột (A, B, C, ...)

        Returns:
            Chỉ số cột (0-based)
        """
        index = 0
        for char in column_name:
            index = index * 26 + (ord(char.upper()) - ord('A') + 1)
        return index - 1

    def index_to_column_letter(self, index):
        """
        Chuyển đổi chỉ số cột (0, 1, 2, ...) sang chữ cái cột (A, B, C, ...)

        Args:
            index: Chỉ số cột (bắt đầu từ 0)

        Returns:
            Chữ cái cột tương ứng (A, B, C, ...)
        """
        result = ""
        while True:
            index, remainder = divmod(index, 26)
            result = chr(65 + remainder) + result
            if index == 0:
                break
            index -= 1
        return result

    def apply_header_mapping(self, source_data, source_headers, column_mapping, normalize_header_func=None):
        """
        Áp dụng mapping header cho dữ liệu nguồn sang dữ liệu đích theo vị trí cột chính xác

        Args:
            source_data: Dữ liệu nguồn (danh sách các hàng)
            source_headers: Danh sách header của sheet nguồn
            column_mapping: Mapping từ header đã chuẩn hóa sang thông tin cột đích (vị trí, tên header)
            normalize_header_func: Hàm chuẩn hóa header (tiếng Việt không dấu, bỏ khoảng trắng, etc.)

        Returns:
            Dữ liệu đã được mapping chính xác theo thứ tự cột đích
        """
        if not column_mapping or not source_headers or not source_data:
            print("Không đủ thông tin để mapping dữ liệu")
            return source_data

        # Chuẩn hóa header nguồn nếu có hàm chuẩn hóa
        normalized_headers = source_headers
        if normalize_header_func:
            normalized_headers = [normalize_header_func(h) for h in source_headers]

        # In thông tin debug để kiểm tra
        print(f"Header nguồn: {source_headers}")
        print(f"Header nguồn sau chuẩn hóa: {normalized_headers}")

        # Hiển thị thông tin header chuẩn hóa trong mapping
        print("Header chuẩn hóa trong mapping:")
        for norm_header, info in column_mapping.items():
            target_position = info['position']
            target_col_letter = self.index_to_column_letter(target_position)
            print(f"  '{norm_header}' -> vị trí {target_col_letter} (index {target_position}), header gốc: '{info['header']}'")

        # So khớp header nguồn với header trong mapping
        exact_match_mapping = {}  # {source_idx: target_position}
        fuzzy_match_mapping = {}  # {source_idx: target_position} cho các trường hợp khớp một phần

        # Kiểm tra các header nguồn và tìm header khớp trong mapping
        for source_idx, header in enumerate(source_headers):
            norm_header = header
            if normalize_header_func:
                norm_header = normalize_header_func(header)

            # Kiểm tra khớp chính xác
            if norm_header in column_mapping:
                target_info = column_mapping[norm_header]
                target_position = target_info["position"]
                exact_match_mapping[source_idx] = target_position
                source_col_letter = self.index_to_column_letter(source_idx)
                target_col_letter = self.index_to_column_letter(target_position)
                print(f"Khớp chính xác: '{header}' (cột {source_col_letter}) -> '{target_info['header']}' (cột {target_col_letter})")
            else:
                # Kiểm tra khớp một phần (chứa cùng từ khóa)
                for norm_key, target_info in column_mapping.items():
                    # Nếu header nguồn chứa ít nhất 80% từ khóa trong header đích hoặc ngược lại
                    if (norm_header in norm_key and len(norm_header) >= 0.8 * len(norm_key)) or \
                       (norm_key in norm_header and len(norm_key) >= 0.8 * len(norm_header)):
                        target_position = target_info["position"]
                        fuzzy_match_mapping[source_idx] = target_position
                        source_col_letter = self.index_to_column_letter(source_idx)
                        target_col_letter = self.index_to_column_letter(target_position)
                        print(f"Khớp một phần: '{header}' (cột {source_col_letter}) -> '{target_info['header']}' (cột {target_col_letter})")
                        break

        # Kết hợp mapping chính xác và mapping một phần, ưu tiên mapping chính xác
        header_mapping = {**fuzzy_match_mapping, **exact_match_mapping}

        # In ra tổng quan về mapping cuối cùng
        print(f"Tổng cộng có {len(header_mapping)} cột được map từ {len(source_headers)} cột nguồn")
        for source_idx, target_idx in sorted(header_mapping.items()):
            source_col_letter = self.index_to_column_letter(source_idx)
            target_col_letter = self.index_to_column_letter(target_idx)
            print(f"  Cột nguồn {source_col_letter} ('{source_headers[source_idx]}') -> Cột đích {target_col_letter}")

        # Xác định số lượng cột cần thiết trong kết quả (dựa trên vị trí cột lớn nhất)
        max_target_position = 0
        for mapping_info in column_mapping.values():
            max_target_position = max(max_target_position, mapping_info["position"])
        result_columns = max_target_position + 1

        # Với mỗi dòng dữ liệu, tạo một dòng mới theo mapping
        result_data = []
        for row in source_data:
            # Tạo dòng mới với số cột bằng với số cột tối đa trong đích
            new_row = [""] * result_columns

            # Áp dụng mapping từ nguồn sang đích
            for source_idx, target_idx in header_mapping.items():
                if source_idx < len(row):
                    new_row[target_idx] = row[source_idx]

            result_data.append(new_row)

        return result_data

    def expand_sheet_if_needed(self, spreadsheet_id, sheet_name, required_rows, required_columns):
        """
        Mở rộng sheet nếu kích thước hiện tại không đủ

        Args:
            spreadsheet_id: ID của spreadsheet
            sheet_name: Tên sheet cần mở rộng
            required_rows: Số dòng tối thiểu cần có
            required_columns: Số cột tối thiểu cần có

        Returns:
            True nếu thành công, False nếu có lỗi
        """
        try:
            # Lấy thông tin về sheet hiện tại
            spreadsheet = self.sheet_manager.open_by_key(spreadsheet_id)
            worksheet = spreadsheet.worksheet(sheet_name)

            # Lấy kích thước hiện tại của sheet
            current_rows = worksheet.row_count
            current_columns = worksheet.col_count

            print(f"Sheet '{sheet_name}' hiện tại có {current_rows} dòng và {current_columns} cột")

            # Kiểm tra xem cần mở rộng không
            resize_needed = False
            new_rows = current_rows
            new_columns = current_columns

            if required_rows > current_rows:
                new_rows = max(required_rows, current_rows * 2)  # Nhân đôi kích thước nếu cần nhiều hơn
                resize_needed = True
                print(f"Cần mở rộng số dòng từ {current_rows} lên {new_rows}")

            if required_columns > current_columns:
                new_columns = max(required_columns, current_columns * 2)  # Nhân đôi kích thước nếu cần nhiều hơn
                resize_needed = True
                print(f"Cần mở rộng số cột từ {current_columns} lên {new_columns}")

            # Nếu cần mở rộng, thực hiện resize
            if resize_needed:
                print(f"Đang mở rộng sheet '{sheet_name}' lên {new_rows} dòng và {new_columns} cột")
                worksheet.resize(rows=new_rows, cols=new_columns)
                print(f"Đã mở rộng sheet thành công")

            return True

        except Exception as e:
            print(f"Lỗi khi mở rộng sheet: {str(e)}")
            return False

    def update_sheet_in_batches(self, spreadsheet_id, sheet_name, data, start_row, headers=None, batch_size=50):
        """
        Cập nhật dữ liệu vào sheet theo từng batch với mixed mode (RAW cho validation, USER_ENTERED cho số)

        Args:
            spreadsheet_id: ID của spreadsheet
            sheet_name: Tên sheet cần cập nhật
            data: Dữ liệu cần cập nhật (list of lists)
            start_row: Dòng bắt đầu cập nhật (1-indexed)
            headers: Danh sách headers để nhận diện cột validation (optional)
            batch_size: Số dòng tối đa cho mỗi batch

        Returns:
            True nếu cập nhật thành công, False nếu có lỗi
        """
        try:
            if not data:
                print("Không có dữ liệu để cập nhật")
                return True

            total_rows = len(data)

            # Tính toán số cột tối đa trong dữ liệu
            max_cols = 0
            for row in data:
                max_cols = max(max_cols, len(row))

            # Mở rộng sheet nếu cần để đảm bảo đủ dòng và cột
            required_rows = start_row + total_rows
            required_columns = max_cols

            if not self.expand_sheet_if_needed(spreadsheet_id, sheet_name, required_rows, required_columns):
                print("Không thể mở rộng sheet để đáp ứng kích thước dữ liệu")
                return False

            num_batches = (total_rows + batch_size - 1) // batch_size  # Làm tròn lên

            # Xác định các cột validation nếu có headers
            validation_columns = []
            if headers:
                for col_idx, header in enumerate(headers):
                    if self.is_validation_column(header):
                        validation_columns.append(col_idx)
                        print(f"Phát hiện cột validation tại index {col_idx}: {header[:50]}...")

            print(f"Cập nhật {total_rows} dòng vào sheet '{sheet_name}' với {num_batches} batch (mỗi batch {batch_size} dòng)")
            if validation_columns:
                print(f"Sử dụng mixed mode: RAW cho {len(validation_columns)} cột validation, USER_ENTERED cho các cột khác")
            else:
                print("Sử dụng USER_ENTERED cho tất cả các cột")

            success_count = 0
            for batch_num in range(num_batches):
                start_idx = batch_num * batch_size
                end_idx = min((batch_num + 1) * batch_size, total_rows)
                batch_data = data[start_idx:end_idx]

                # Tính toán range cho batch này
                batch_start_row = start_row + start_idx
                batch_end_row = start_row + end_idx - 1

                # Tính toán chữ cái cột cuối cùng dựa trên số cột tối đa trong batch
                if batch_data:
                    # Tìm số cột tối đa trong tất cả các dòng của batch này
                    max_cols_in_batch = 0
                    for row in batch_data:
                        max_cols_in_batch = max(max_cols_in_batch, len(row))

                    # Chuẩn hóa tất cả các dòng để có cùng số cột và chuẩn hóa định dạng số
                    if max_cols_in_batch > 0:
                        for i, row in enumerate(batch_data):
                            # Chỉ chuẩn hóa định dạng số cho từng ô trong dòng
                            normalized_row = []
                            for cell_value in row:
                                normalized_value = self.normalize_number_format(cell_value)
                                normalized_row.append(normalized_value)

                            # Thêm các ô trống để đảm bảo tất cả dòng có cùng số cột
                            if len(normalized_row) < max_cols_in_batch:
                                normalized_row = normalized_row + [""] * (max_cols_in_batch - len(normalized_row))

                            batch_data[i] = normalized_row

                        num_cols = max_cols_in_batch
                        last_col = self.index_to_column_letter(num_cols - 1)
                    else:
                        last_col = "A"  # Default nếu không có dữ liệu
                else:
                    last_col = "A"  # Default nếu không có dữ liệu

                batch_range = f"{sheet_name}!A{batch_start_row}:{last_col}{batch_end_row}"
                print(f"  Batch {batch_num+1}/{num_batches}: Cập nhật range {batch_range} ({len(batch_data)} dòng, {num_cols} cột)")

                try:
                    # Kiểm tra tính nhất quán của dữ liệu trước khi cập nhật
                    for row_idx, row in enumerate(batch_data):
                        if len(row) != num_cols:
                            print(f"  Cảnh báo: Dòng {row_idx} có {len(row)} cột, khác với {num_cols} cột dự kiến")

                    # Xử lý mixed mode: tách dữ liệu thành validation columns và non-validation columns
                    if validation_columns:
                        # Cập nhật non-validation columns với USER_ENTERED
                        non_validation_data = []
                        for row in batch_data:
                            new_row = row.copy()
                            # Xóa dữ liệu validation columns để cập nhật riêng
                            for col_idx in sorted(validation_columns, reverse=True):
                                if col_idx < len(new_row):
                                    new_row[col_idx] = ""  # Tạm thời để trống
                            non_validation_data.append(new_row)

                        # Cập nhật tất cả với USER_ENTERED (validation columns sẽ trống)
                        update_body = {
                            'values': non_validation_data
                        }

                        self.sheets_service.spreadsheets().values().update(
                            spreadsheetId=spreadsheet_id,
                            range=batch_range,
                            valueInputOption='USER_ENTERED',
                            body=update_body
                        ).execute()

                        # Cập nhật từng validation column với RAW mode
                        for col_idx in validation_columns:
                            col_letter = self.index_to_column_letter(col_idx)

                            # Lấy dữ liệu của cột validation này
                            column_data = []
                            for row in batch_data:
                                if col_idx < len(row):
                                    column_data.append([row[col_idx]])
                                else:
                                    column_data.append([""])

                            # Tính range cho cột validation này
                            col_range = f"{sheet_name}!{col_letter}{batch_start_row}:{col_letter}{batch_end_row}"

                            # Cập nhật với RAW mode
                            validation_body = {
                                'values': column_data
                            }

                            self.sheets_service.spreadsheets().values().update(
                                spreadsheetId=spreadsheet_id,
                                range=col_range,
                                valueInputOption='RAW',
                                body=validation_body
                            ).execute()

                            print(f"    Cập nhật cột validation {col_letter} với RAW mode")
                            time.sleep(0.1)  # Tạm dừng ngắn giữa các cột
                    else:
                        # Không có validation columns, sử dụng USER_ENTERED cho tất cả
                        update_body = {
                            'values': batch_data
                        }

                        self.sheets_service.spreadsheets().values().update(
                            spreadsheetId=spreadsheet_id,
                            range=batch_range,
                            valueInputOption='USER_ENTERED',
                            body=update_body
                        ).execute()

                    success_count += 1

                    # Tạm dừng một chút để tránh quá tải API
                    time.sleep(0.5)

                except Exception as batch_error:
                    print(f"  Lỗi khi cập nhật batch {batch_num+1}: {str(batch_error)}")
                    print(f"  Range được yêu cầu: {batch_range}")
                    print(f"  Số dòng dữ liệu: {len(batch_data)}")
                    if batch_data:
                        print(f"  Số cột trong dòng đầu tiên: {len(batch_data[0])}")
                        print(f"  Số cột tối đa trong batch: {num_cols}")

                    # Thử cập nhật lại với kích thước nhỏ hơn nếu lỗi liên quan đến grid limits
                    if "exceeds grid limits" in str(batch_error).lower():
                        print("  Đây là lỗi grid limits, thử mở rộng sheet và cập nhật lại...")

                        # Cố gắng mở rộng sheet lần nữa
                        self.expand_sheet_if_needed(spreadsheet_id, sheet_name, batch_end_row + 50, num_cols + 10)

                        # Thử lại sau khi mở rộng
                        try:
                            self.sheets_service.spreadsheets().values().update(
                                spreadsheetId=spreadsheet_id,
                                range=batch_range,
                                valueInputOption='USER_ENTERED',
                                body=update_body
                            ).execute()

                            success_count += 1
                            print("  Cập nhật lại thành công sau khi mở rộng sheet")

                        except Exception as retry_error:
                            print(f"  Vẫn không thể cập nhật sau khi mở rộng: {str(retry_error)}")

            print(f"Đã hoàn thành {success_count}/{num_batches} batch, cập nhật {total_rows} dòng vào sheet '{sheet_name}'")
            return success_count == num_batches  # Thành công nếu tất cả các batch đều OK

        except Exception as e:
            print(f"Lỗi tổng thể khi cập nhật batch: {str(e)}")
            return False

    def find_last_data_row(self, spreadsheet_id, sheet_name, columns=None):
        """
        Tìm dòng cuối cùng có dữ liệu của các cột chỉ định

        Args:
            spreadsheet_id: ID của spreadsheet
            sheet_name: Tên sheet cần kiểm tra
            columns: Danh sách các cột cần kiểm tra (mặc định là G, H)

        Returns:
            Số dòng cuối cùng có dữ liệu (bắt đầu từ 1)
        """
        try:
            if columns is None:
                columns = ['G', 'H']  # Mặc định kiểm tra cột G và H

            # Mở spreadsheet và sheet
            spreadsheet = self.sheet_manager.open_by_key(spreadsheet_id)
            sheet = spreadsheet.worksheet(sheet_name)

            last_row = 0

            # Kiểm tra từng cột
            for column in columns:
                # Lấy tất cả các giá trị trong cột
                col_values = sheet.col_values(self.column_to_index(column) + 1)  # +1 vì col_values bắt đầu từ 1

                # Đếm số lượng ô có dữ liệu (không trống) từ dòng 3 trở đi
                # Dòng 1 và 2 là header nên bỏ qua (index 0 và 1)
                non_empty_cells = []
                for i, value in enumerate(col_values):
                    row_number = i + 1  # Số dòng thực tế (1-indexed)
                    if row_number >= 3 and value and value.strip():  # Chỉ kiểm tra từ dòng 3 và ô không trống
                        non_empty_cells.append(row_number)

                # Xác định dòng cuối cùng có dữ liệu
                if non_empty_cells:
                    last_row_in_col = max(non_empty_cells)
                    last_row = max(last_row, last_row_in_col)
                    print(f"Dòng cuối cùng có dữ liệu trong cột {column}: {last_row_in_col}")

            print(f"Dòng cuối cùng có dữ liệu trong sheet nguồn (cột G và H): {last_row}")
            return last_row

        except Exception as e:
            print(f"Lỗi khi tìm dòng cuối cùng: {str(e)}")
            return 0

    def add_more_marker_and_color(self, spreadsheet_id, sheet_name, row_number):
        """
        Thêm chữ "More" vào cột C và tô màu đỏ cho toàn bộ dòng

        Args:
            spreadsheet_id: ID của spreadsheet
            sheet_name: Tên sheet cần cập nhật
            row_number: Số thứ tự dòng cần thêm "More" và tô màu

        Returns:
            True nếu thành công, False nếu có lỗi
        """
        try:
            # Mở spreadsheet
            spreadsheet = self.sheet_manager.open_by_key(spreadsheet_id)
            sheet = spreadsheet.worksheet(sheet_name)

            # Xác định số cột trong sheet để tô màu toàn bộ dòng
            col_count = sheet.col_count

            # Thêm chữ "More" vào cột C
            range_name = f"{sheet_name}!C{row_number}"
            self.sheets_service.spreadsheets().values().update(
                spreadsheetId=spreadsheet_id,
                range=range_name,
                valueInputOption='USER_ENTERED',
                body={'values': [["More"]]}
            ).execute()

            print(f"Đã thêm chữ 'More' vào ô C{row_number}")

            # Tô màu đỏ cho toàn bộ dòng
            # Định nghĩa màu đỏ (RGB)
            red_color = {
                "red": 1.0,
                "green": 0.0,
                "blue": 0.0
            }

            # Tạo request để tô màu
            requests = [{
                "updateCells": {
                    "range": {
                        "sheetId": sheet.id,
                        "startRowIndex": row_number - 1,  # API bắt đầu từ 0
                        "endRowIndex": row_number,
                        "startColumnIndex": 0,
                        "endColumnIndex": col_count
                    },
                    "rows": [{
                        "values": [{
                            "userEnteredFormat": {
                                "backgroundColor": red_color
                            }
                        }] * col_count
                    }],
                    "fields": "userEnteredFormat.backgroundColor"
                }
            }]

            # Thực hiện request
            self.sheets_service.spreadsheets().batchUpdate(
                spreadsheetId=spreadsheet_id,
                body={"requests": requests}
            ).execute()

            print(f"Đã tô màu đỏ cho dòng {row_number}")
            return True

        except Exception as e:
            print(f"Lỗi khi thêm 'More' và tô màu: {str(e)}")
            return False

    def delete_row(self, spreadsheet_id, sheet_name, row_number):
        """
        Xóa một dòng trong Google Sheet

        Args:
            spreadsheet_id: ID của spreadsheet
            sheet_name: Tên sheet cần xóa dòng
            row_number: Số thứ tự dòng cần xóa (bắt đầu từ 1)

        Returns:
            True nếu thành công, False nếu có lỗi
        """
        try:
            # Mở spreadsheet
            spreadsheet = self.sheet_manager.open_by_key(spreadsheet_id)
            sheet = spreadsheet.worksheet(sheet_name)

            # Tạo request để xóa dòng (API bắt đầu từ 0 nên phải trừ 1)
            requests = [{
                "deleteDimension": {
                    "range": {
                        "sheetId": sheet.id,
                        "dimension": "ROWS",
                        "startIndex": row_number - 1,  # API bắt đầu từ 0
                        "endIndex": row_number  # endIndex không bao gồm trong phạm vi
                    }
                }
            }]

            # Thực hiện request
            self.sheets_service.spreadsheets().batchUpdate(
                spreadsheetId=spreadsheet_id,
                body={"requests": requests}
            ).execute()

            print(f"Đã xóa dòng {row_number} trong sheet '{sheet_name}'")
            return True

        except Exception as e:
            print(f"Lỗi khi xóa dòng: {str(e)}")
            return False

    def copy_data_to_target_sheet(self, target_spreadsheet_id, target_sheet_title, source_data, column_mapping=None, normalize_header_func=None, mode="copy"):
        """
        Copy hoặc append dữ liệu từ các sheet nguồn sang sheet đích

        Args:
            target_spreadsheet_id: ID của spreadsheet chứa sheet đích
            target_sheet_title: Tên của sheet đích
            source_data: Danh sách các nguồn dữ liệu, mỗi nguồn là dict với các key:
                         - spreadsheet_id: ID của spreadsheet nguồn
                         - sheet_name: Tên sheet nguồn
                         - start_row: Dòng bắt đầu lấy dữ liệu (tính từ 1)
                         - end_row: Dòng kết thúc lấy dữ liệu (nếu None sẽ lấy đến hết)
            column_mapping: Mapping từ header nguồn sang header đích (dict)
            normalize_header_func: Hàm chuẩn hóa header (tiếng Việt không dấu, bỏ khoảng trắng, etc.)
            mode: Chế độ import dữ liệu: "copy" (tạo mới), "append" (bổ sung dữ liệu)
        """
        try:
            # Lấy header của sheet đích (từ dòng 3)
            range_name = f"{target_sheet_title}!A3:ZZ3"
            result = self.sheets_service.spreadsheets().values().get(
                spreadsheetId=target_spreadsheet_id,
                range=range_name
            ).execute()
            target_headers = result.get('values', [[]])[0]

            print(f"Sheet đích '{target_sheet_title}' có {len(target_headers)} cột header")

            # Dòng hiện tại để chèn dữ liệu vào sheet đích
            if mode == "append":
                # Tìm dòng cuối cùng có dữ liệu trong sheet đích
                last_row = self.find_last_data_row(target_spreadsheet_id, target_sheet_title)
                if last_row > 0:
                    # Bắt đầu từ dòng tiếp theo sau dòng cuối cùng có dữ liệu
                    current_row = last_row + 1
                    print(f"Chế độ Append: Bắt đầu từ dòng {current_row} (sau dòng cuối cùng có dữ liệu)")
                else:
                    # Nếu không tìm thấy dữ liệu, bắt đầu từ dòng 4 như chế độ copy
                    current_row = 4
                    print("Chế độ Append: Không tìm thấy dữ liệu trong sheet đích, bắt đầu từ dòng 4")
            else:
                # Chế độ copy: xóa dữ liệu hiện có và bắt đầu từ dòng 4
                print("Chế độ Copy: Xóa dữ liệu hiện có và bắt đầu từ dòng 4")

                # Xóa toàn bộ dữ liệu hiện có trong sheet đích (từ dòng 4 trở đi)
                last_row = self.find_last_data_row(target_spreadsheet_id, target_sheet_title)
                if last_row >= 4:
                    print(f"Xóa dữ liệu từ dòng 4 đến dòng {last_row}")
                    clear_range = f"{target_sheet_title}!A4:ZZ{last_row}"
                    self.sheets_service.spreadsheets().values().clear(
                        spreadsheetId=target_spreadsheet_id,
                        range=clear_range
                    ).execute()
                    print("Đã xóa dữ liệu cũ trong sheet đích")

                current_row = 4

            # Danh sách các header mà dữ liệu sẽ bị bỏ qua
            skip_data_headers = [
                "KOL Pick",
                "Review/ATC",
                "Khung giờ sản phẩm được review"
            ]

            for source in source_data:
                spreadsheet_id = source.get('spreadsheet_id')
                sheet_name = source.get('sheet_name')
                start_row = source.get('start_row', 3)  # Mặc định bắt đầu từ dòng 3 (bỏ qua header ở dòng 2)
                end_row = source.get('end_row')  # Nếu None sẽ lấy đến hết

                print(f"Đang xử lý sheet '{sheet_name}' từ spreadsheet: {spreadsheet_id}")

                # Mở spreadsheet và sheet nguồn
                source_spreadsheet = self.sheet_manager.open_by_key(spreadsheet_id)
                source_sheet = source_spreadsheet.worksheet(sheet_name)

                # Lấy headers của sheet nguồn (từ dòng 2)
                range_name = f"{sheet_name}!A2:ZZ2"
                result = self.sheets_service.spreadsheets().values().get(
                    spreadsheetId=spreadsheet_id,
                    range=range_name
                ).execute()
                source_headers = result.get('values', [[]])[0]

                print(f"Sheet nguồn '{sheet_name}' có {len(source_headers)} cột header")

                # Hiển thị header nguồn để kiểm tra
                print("Header nguồn:")
                for i, header in enumerate(source_headers):
                    print(f"  [{i}]: '{header}'")

                # Tìm vị trí của các cột cần bỏ qua dữ liệu
                skip_data_indices = []
                for i, header in enumerate(source_headers):
                    if header in skip_data_headers:
                        skip_data_indices.append(i)
                        print(f"Sẽ bỏ qua dữ liệu ở cột '{header}' (vị trí {i+1})")

                # Xác định vị trí dòng bắt đầu và kết thúc dựa trên chế độ
                if mode == "append":
                    # Trong chế độ append, tìm dòng "More" trong sheet nguồn
                    more_row = None

                    # Tìm tất cả các ô có giá trị "More" trong cột C (index 2)
                    col_values = source_sheet.col_values(3)  # Cột C (3rd column)
                    for i, value in enumerate(col_values):
                        if value == "More":
                            more_row = i + 1  # Đổi từ 0-based sang 1-based index
                            print(f"Tìm thấy dòng 'More' tại vị trí {more_row} trong sheet '{sheet_name}'")
                            break

                    if more_row:
                        # Nếu tìm thấy dòng "More", bắt đầu từ dòng tiếp theo
                        start_row = more_row + 1
                        print(f"Chế độ Append: Bắt đầu lấy dữ liệu từ dòng {start_row} (sau dòng 'More')")

                # Lấy tổng số dòng trong sheet nguồn
                if end_row is None:
                    # Lấy tổng số dòng có dữ liệu
                    all_values = source_sheet.get_all_values()
                    end_row = len(all_values)

                # Lấy dữ liệu từ sheet nguồn
                range_name = f"{sheet_name}!A{start_row}:ZZ{end_row}"
                result = self.sheets_service.spreadsheets().values().get(
                    spreadsheetId=spreadsheet_id,
                    range=range_name
                ).execute()

                values = result.get('values', [])

                if not values:
                    print(f"Không có dữ liệu trong sheet '{sheet_name}'")
                    continue

                print(f"Đã lấy {len(values)} dòng dữ liệu từ sheet '{sheet_name}'")

                # Xóa dữ liệu từ các cột cần bỏ qua
                if skip_data_indices:
                    for row in values:
                        for idx in sorted(skip_data_indices, reverse=True):
                            if idx < len(row):
                                row[idx] = ""  # Thay giá trị bằng chuỗi rỗng

                # Áp dụng mapping header nếu có
                mapped_data = []
                if column_mapping:
                    print(f"Áp dụng mapping header cho dữ liệu từ sheet '{sheet_name}'")
                    mapped_data = self.apply_header_mapping(values, source_headers, column_mapping, normalize_header_func)

                    # Kiểm tra số lượng dòng dữ liệu đã mapping
                    if len(mapped_data) > 0:
                        print(f"Dữ liệu sau khi mapping: {len(mapped_data)} dòng, {len(mapped_data[0])} cột")

                        # Kiểm tra dữ liệu đầu tiên sau khi mapping
                        print("Mẫu dữ liệu đầu tiên sau mapping:")
                        if mapped_data:
                            sample_row = mapped_data[0]
                            for i, value in enumerate(sample_row):
                                if value:  # Chỉ hiển thị các giá trị không trống
                                    print(f"  Cột {i}: '{value}'")
                else:
                    print("Không có mapping được áp dụng, sử dụng dữ liệu gốc")
                    mapped_data = values

                # Chèn dữ liệu vào sheet đích sử dụng batch update
                if mapped_data:
                    print(f"Bắt đầu cập nhật {len(mapped_data)} dòng dữ liệu vào sheet đích từ dòng {current_row}")

                    # Sử dụng phương thức batch update với mixed mode
                    self.update_sheet_in_batches(
                        target_spreadsheet_id,
                        target_sheet_title,
                        mapped_data,
                        current_row,
                        headers=target_headers,  # Truyền headers để nhận diện validation columns
                        batch_size=50  # Mỗi batch tối đa 50 dòng
                    )

                    # Cập nhật dòng hiện tại
                    current_row += len(mapped_data)

                    # Trong chế độ Copy, thêm "More" và tô màu đỏ vào sheet nguồn sau khi xử lý xong
                    if mode == "copy":
                        # Tìm dòng cuối cùng có dữ liệu trong sheet nguồn
                        last_row = self.find_last_data_row(spreadsheet_id, sheet_name)
                        if last_row > 0:
                            # Dòng tiếp theo sau dòng cuối cùng có dữ liệu
                            next_row = last_row + 1
                            print(f"Dòng cuối cùng có dữ liệu trong sheet nguồn: {last_row}")
                            print(f"Cần thêm 'More' vào sheet nguồn sau dòng này (dòng {next_row})")

                            # Thêm "More" và tô màu đỏ vào SHEET NGUỒN
                            self.add_more_marker_and_color(spreadsheet_id, sheet_name, next_row)
                            print(f"Đã thêm 'More' và tô màu đỏ ở dòng {next_row} trong sheet nguồn '{sheet_name}'")
                    # Trong chế độ Append, xóa dòng "More" cũ và thêm dòng "More" mới
                    elif mode == "append":
                        # Xóa dòng "More" cũ nếu đã tìm thấy
                        if more_row:
                            print(f"Chế độ Append: Xóa dòng 'More' cũ ở vị trí {more_row}")
                            self.delete_row(spreadsheet_id, sheet_name, more_row)

                        # Tìm dòng cuối cùng có dữ liệu trong sheet nguồn
                        last_row = self.find_last_data_row(spreadsheet_id, sheet_name)
                        if last_row > 0:
                            # Dòng tiếp theo sau dòng cuối cùng có dữ liệu
                            next_row = last_row + 1
                            print(f"Dòng cuối cùng có dữ liệu trong sheet nguồn: {last_row}")
                            print(f"Cần thêm 'More' mới vào sheet nguồn sau dòng này (dòng {next_row})")

                            # Thêm "More" và tô màu đỏ vào SHEET NGUỒN
                            self.add_more_marker_and_color(spreadsheet_id, sheet_name, next_row)
                            print(f"Đã thêm 'More' và tô màu đỏ ở dòng {next_row} trong sheet nguồn '{sheet_name}'")

            print(f"Hoàn thành việc copy dữ liệu vào sheet '{target_sheet_title}'")
            return True

        except Exception as e:
            print(f"Lỗi khi copy dữ liệu: {str(e)}")
            return False

    def create_column_mapping_from_target_sheet(self, target_spreadsheet_id, target_sheet_title, normalize_header_func=None):
        """
        Tạo mapping cột dựa trên header thực tế của sheet đích

        Args:
            target_spreadsheet_id: ID của spreadsheet đích
            target_sheet_title: Tên sheet đích
            normalize_header_func: Hàm chuẩn hóa header (nếu có)

        Returns:
            Dict mapping từ header chuẩn hóa sang thông tin cột đích (vị trí, tên header)
        """
        try:
            # Đọc header từ sheet đích (dòng 3)
            range_name = f"{target_sheet_title}!A3:ZZ3"
            result = self.sheets_service.spreadsheets().values().get(
                spreadsheetId=target_spreadsheet_id,
                range=range_name
            ).execute()
            target_headers = result.get('values', [[]])[0]

            if not target_headers:
                print(f"Không tìm thấy header trong sheet đích '{target_sheet_title}'")
                return {}

            print(f"\nĐọc được {len(target_headers)} header từ sheet đích '{target_sheet_title}'")

            # Tạo mapping từ header chuẩn hóa sang (vị trí, header gốc) trong sheet đích
            exact_mapping = {}

            # Chuẩn hóa header nếu có hàm chuẩn hóa
            hdr_normalize = normalize_header_func if normalize_header_func else lambda x: x

            # Hiển thị thông tin về thứ tự header trong sheet đích
            print("\nCấu hình vị trí cột thực tế trong sheet đích:")
            for index, header in enumerate(target_headers):
                if not header.strip():  # Bỏ qua header trống
                    continue

                column_letter = self.index_to_column_letter(index)
                print(f"  Cột {column_letter} (index {index}): {header}")

                # Chuẩn hóa header và lưu vào mapping
                normalized = hdr_normalize(header)
                if normalized:  # Chỉ lưu header có giá trị sau khi chuẩn hóa
                    exact_mapping[normalized] = {
                        "position": index,  # Vị trí trong mảng (bắt đầu từ 0)
                        "header": header    # Header gốc
                    }

            return exact_mapping

        except Exception as e:
            print(f"Lỗi khi tạo mapping từ sheet đích: {str(e)}")
            return {}

    def process_data_import(self, target_spreadsheet_id, source_data, target_sheet_title=None, column_mapping=None, normalize_header_func=None, mode="copy"):
        """
        Xử lý toàn bộ quá trình import dữ liệu

        Args:
            target_spreadsheet_id: ID của spreadsheet đích
            source_data: Danh sách các nguồn dữ liệu (mỗi nguồn cần có spreadsheet_id và sheet_name)
                         Dữ liệu nguồn sẽ được lấy từ dòng 3, với header ở dòng 2
            target_sheet_title: Tên sheet đích. Nếu None thì mặc định là "Pool Deal"
                                Nếu sheet không tồn tại, sẽ được tạo từ template
                                Dữ liệu đích sẽ được chèn từ dòng 4, với header ở dòng 3
            column_mapping: Mapping từ header nguồn sang header đích (dict)
                           Nếu None, sẽ tạo mapping tự động từ sheet đích thực tế
            normalize_header_func: Hàm chuẩn hóa header tiếng Việt (không dấu, không space)
            mode: Chế độ import: "copy" (tạo mới) hoặc "append" (bổ sung dữ liệu)

        Returns:
            True nếu thành công, False nếu thất bại
        """
        try:
            # Sử dụng tên sheet mặc định nếu không được chỉ định
            if target_sheet_title is None or target_sheet_title.strip() == "":
                target_sheet_title = DEFAULT_TARGET_SHEET

            # Kiểm tra xem sheet đích có tồn tại không
            sheet_exists = False

            # Mở spreadsheet đích
            target_spreadsheet = self.sheet_manager.open_by_key(target_spreadsheet_id)
            worksheet_list = target_spreadsheet.worksheets()

            for sheet in worksheet_list:
                if sheet.title == target_sheet_title:
                    sheet_exists = True
                    break

            # Chỉ tạo sheet mới nếu sheet không tồn tại
            if not sheet_exists:
                print(f"Sheet '{target_sheet_title}' không tồn tại, tạo mới từ template...")
                sheet_id = self.create_sheet_from_template(target_spreadsheet_id, target_sheet_title)
                if not sheet_id:
                    return False

                # Chờ một chút để đảm bảo sheet đã được tạo hoàn tất
                time.sleep(2)
            else:
                print(f"Sử dụng sheet đã tồn tại: '{target_sheet_title}'")

            # Đảm bảo các thông tin nguồn đã đúng định dạng
            for source in source_data:
                # Đặt start_row mặc định là 3 (dòng dữ liệu đầu tiên trong sheet nguồn, sau header ở dòng 2)
                if 'start_row' not in source:
                    source['start_row'] = 3

            # Tạo column mapping từ sheet đích thực tế nếu chưa được cung cấp
            if not column_mapping:
                print("Không có mapping được cung cấp, tạo mapping từ sheet đích thực tế...")
                column_mapping = self.create_column_mapping_from_target_sheet(
                    target_spreadsheet_id,
                    target_sheet_title,
                    normalize_header_func
                )
                if not column_mapping:
                    print("Không thể tạo mapping từ sheet đích")
                    return False
            else:
                print(f"Sử dụng mapping đã được cung cấp trước ({len(column_mapping)} cột)")

            # Copy/Append dữ liệu vào sheet đích
            result = self.copy_data_to_target_sheet(
                target_spreadsheet_id,
                target_sheet_title,
                source_data,
                column_mapping,
                normalize_header_func,
                mode
            )

            if result:
                print("Import dữ liệu thành công!")
                # Không tự động xử lý dữ liệu - để người dùng chọn điều kiện trong bước 2

            return result

        except Exception as e:
            print(f"Lỗi khi xử lý import dữ liệu: {str(e)}")
            return False

    def process_imported_data(self, spreadsheet_id, sheet_title):
        """
        Bước 2: Xử lý dữ liệu sau khi đã import

        Args:
            spreadsheet_id: ID của spreadsheet đích
            sheet_title: Tên sheet đích đã import dữ liệu

        Returns:
            True nếu thành công, False nếu thất bại
        """
        try:
            print(f"Bắt đầu xử lý dữ liệu trên sheet '{sheet_title}'")

            # Lấy header của sheet đích (dòng 3)
            range_name = f"{sheet_title}!A3:ZZ3"
            result = self.sheets_service.spreadsheets().values().get(
                spreadsheetId=spreadsheet_id,
                range=range_name
            ).execute()
            headers = result.get('values', [[]])[0]

            if not headers:
                print("Không tìm thấy headers trong sheet đích")
                return False

            print(f"Tìm thấy {len(headers)} cột trong sheet đích")

            # Xác định các cột cần xử lý dựa trên loại dữ liệu
            number_columns = []  # Cột chứa số liệu cần định dạng
            kvalue_columns = []  # Cột có thể chứa giá trị 'k' cần chuyển đổi
            text_columns = []    # Cột văn bản cần xử lý xuống dòng
            percent_columns = [] # Cột chứa phần trăm cần định dạng
            x_value_columns = [] # Cột chứa giá trị X/Không

            # Danh sách các từ khóa để xác định loại cột
            number_keywords = ['gia', 'price', 'amount', 'chi phi', 'chiphi', 'tong tien', 'tongtien', 'value', 'cost']
            kvalue_keywords = ['gia', 'price', 'amount', 'chi phi', 'chiphi', 'tong tien', 'tongtien', 'value']
            text_keywords = ['mota', 'ghichu', 'note', 'description', 'desc', 'noidung', 'comment']
            percent_keywords = ['percent', 'phantram', 'rate', 'tile', 'ti le']
            x_value_keywords = ['trangthai', 'status', 'state', 'present', 'attendance']

            # Phân loại các cột dựa trên tên header
            for idx, header in enumerate(headers):
                if not header:
                    continue

                header_lower = header.lower()

                # Cột chứa giá trị X/Không
                if any(keyword in header_lower for keyword in x_value_keywords):
                    print(f"Cột '{header}' được xác định là cột trạng thái X/Không")
                    x_value_columns.append(idx)

                # Cột chứa số liệu
                if any(keyword in header_lower for keyword in number_keywords):
                    print(f"Cột '{header}' được xác định là cột số liệu")
                    number_columns.append(idx)

                # Cột có thể chứa giá trị k
                if any(keyword in header_lower for keyword in kvalue_keywords):
                    print(f"Cột '{header}' được xác định là cột có thể chứa giá trị 'k'")
                    kvalue_columns.append(idx)

                # Cột văn bản cần xử lý xuống dòng
                if any(keyword in header_lower for keyword in text_keywords):
                    print(f"Cột '{header}' được xác định là cột văn bản")
                    text_columns.append(idx)

                # Cột chứa phần trăm
                if any(keyword in header_lower for keyword in percent_keywords):
                    print(f"Cột '{header}' được xác định là cột phần trăm")
                    percent_columns.append(idx)

            # Lấy toàn bộ dữ liệu trong sheet
            last_row = self.find_last_data_row(spreadsheet_id, sheet_title)
            if last_row <= 3:  # Nếu chỉ có dòng header hoặc ít dữ liệu hơn
                print("Không đủ dữ liệu để xử lý")
                return False

            range_name = f"{sheet_title}!A4:ZZ{last_row}"
            result = self.sheets_service.spreadsheets().values().get(
                spreadsheetId=spreadsheet_id,
                range=range_name,
                valueRenderOption="UNFORMATTED_VALUE"
            ).execute()

            values = result.get('values', [])
            if not values:
                print("Không có dữ liệu để xử lý")
                return False

            print(f"Đã lấy {len(values)} dòng dữ liệu để xử lý")

            # Biến đếm số lượng thay đổi
            total_changes = 0

            # Xử lý từng loại dữ liệu
            # 1. Xử lý các giá trị "X" trong cột trạng thái
            if x_value_columns:
                print("Bước 2.1: Xử lý các giá trị X/Không trong cột trạng thái")
                changes = self._process_x_values(values, x_value_columns)
                total_changes += changes
                print(f"  - Đã xử lý {changes} giá trị X/Không")

            # 2. Định dạng số liệu
            if number_columns:
                print("Bước 2.2: Định dạng số liệu")
                changes = self._process_number_format(values, number_columns)
                total_changes += changes
                print(f"  - Đã định dạng {changes} giá trị số")

            # 3. Chuyển đổi giá trị 'k' thành '000'
            if kvalue_columns:
                print("Bước 2.3: Chuyển đổi giá trị 'k' thành '000'")
                changes = self._process_k_values(values, kvalue_columns)
                total_changes += changes
                print(f"  - Đã chuyển đổi {changes} giá trị 'k'")

            # 4. Xử lý xuống dòng trong văn bản
            if text_columns:
                print("Bước 2.4: Xử lý xuống dòng trong văn bản")
                changes = self._process_line_breaks(values, text_columns)
                total_changes += changes
                print(f"  - Đã xử lý {changes} ô văn bản có xuống dòng")

            # 5. Định dạng phần trăm
            if percent_columns:
                print("Bước 2.5: Định dạng phần trăm")
                changes = self._process_percentages(values, percent_columns)
                total_changes += changes
                print(f"  - Đã định dạng {changes} giá trị phần trăm")

            # 6. Xóa các dòng trống
            print("Bước 2.6: Xóa các dòng trống")
            empty_rows = self._identify_empty_rows(values, list(range(len(headers))))

            if empty_rows:
                print(f"  - Đã xác định được {len(empty_rows)} dòng trống cần xóa")
                # Không xóa dòng trực tiếp vì sẽ làm thay đổi index
                # Thay vào đó, đánh dấu để không cập nhật lại
            else:
                print("  - Không tìm thấy dòng trống")

            # Cập nhật lại dữ liệu vào sheet nếu có thay đổi
            if total_changes > 0 or empty_rows:
                print(f"Tổng cộng có {total_changes} thay đổi, cập nhật lại sheet...")

                # Lọc bỏ các dòng trống nếu có
                if empty_rows:
                    new_values = []
                    for i, row in enumerate(values):
                        if i not in empty_rows:
                            new_values.append(row)
                    values = new_values
                    print(f"  - Đã loại bỏ {len(empty_rows)} dòng trống")
                    total_changes += len(empty_rows)

                # Cập nhật lại dữ liệu vào sheet
                self.update_sheet_in_batches(
                    spreadsheet_id,
                    sheet_title,
                    values,
                    4,  # Bắt đầu từ dòng 4 (sau header ở dòng 3)
                    batch_size=50
                )

                print(f"Hoàn thành bước 2: Đã xử lý và cập nhật {total_changes} thay đổi")
                return True
            else:
                print("Không có thay đổi nào cần cập nhật")
                return True

        except Exception as e:
            print(f"Lỗi khi xử lý dữ liệu (bước 2): {str(e)}")
            return False

    def _process_x_values(self, values, column_indices):
        """Xử lý các giá trị X/Không trong cột trạng thái"""
        changes_count = 0
        values_to_clear = ["x", "X", "không", "Không", "Không có", "No gift", "No", "no"]
        single_char_to_clear = ["x", "X", "/", "-"]

        for row in values:
            for col_idx in column_indices:
                if col_idx < len(row):
                    original_value = row[col_idx]
                    if original_value is None:
                        continue

                    if not isinstance(original_value, str):
                        original_value = str(original_value)

                    if original_value.strip() in values_to_clear or (len(original_value.strip()) == 1 and original_value.strip() in single_char_to_clear):
                        row[col_idx] = ""
                        changes_count += 1

        return changes_count

    def _process_number_format(self, values, column_indices):
        """Định dạng số liệu"""
        changes_count = 0
        for row in values:
            for col_idx in column_indices:
                if col_idx < len(row):
                    original_value = row[col_idx]
                    if original_value is None or original_value == "":
                        continue

                    if not isinstance(original_value, str):
                        original_value = str(original_value)

                    if original_value:
                        # Xóa dấu phẩy, dấu chấm và ký tự đồng VNĐ
                        temp_value = original_value
                        temp_value = temp_value.replace(",", "").replace(".", "")
                        # Xử lý các biến thể của ký tự đồng: đ, Đ, ₫
                        temp_value = temp_value.replace("đ", "").replace("Đ", "").replace("₫", "")
                        # Loại bỏ khoảng trắng thừa có thể xuất hiện sau khi xóa ký tự
                        new_value = temp_value.strip()

                        if new_value != original_value:
                            row[col_idx] = new_value
                            changes_count += 1

        return changes_count

    def _process_k_values(self, values, column_indices):
        """Chuyển đổi giá trị 'k' thành '000'"""
        changes_count = 0
        for row in values:
            for col_idx in column_indices:
                if col_idx < len(row):
                    original_value = row[col_idx]
                    if original_value is None or original_value == "":
                        continue

                    if not isinstance(original_value, str):
                        original_value = str(original_value)

                    if original_value:
                        # Format giá trị và/hoặc chuyển đổi 'k' thành '000'
                        cleaned_value = original_value.replace(",", "").replace(".", "")
                        if "k" in cleaned_value.lower():
                            new_value = cleaned_value.lower().replace("k", "000")
                            row[col_idx] = new_value
                            changes_count += 1
                        elif "," in original_value or "." in original_value:
                            new_value = cleaned_value
                            if new_value != original_value:
                                row[col_idx] = new_value
                                changes_count += 1

        return changes_count

    def _process_line_breaks(self, values, column_indices):
        """Xử lý xuống dòng trong văn bản"""
        changes_count = 0
        for row in values:
            for col_idx in column_indices:
                if col_idx < len(row):
                    original_value = row[col_idx]
                    if original_value is None or original_value == "":
                        continue

                    if not isinstance(original_value, str):
                        original_value = str(original_value)

                    if original_value and '\n' in original_value:
                        # Xử lý xuống dòng (loại bỏ dòng trống)
                        lines = original_value.split('\n')
                        non_empty_lines = [line.strip() for line in lines if line.strip()]
                        new_value = '\n'.join(non_empty_lines)

                        if new_value != original_value:
                            row[col_idx] = new_value
                            changes_count += 1

        return changes_count

    def _process_percentages(self, values, column_indices):
        """Định dạng phần trăm"""
        changes_count = 0
        for row in values:
            for col_idx in column_indices:
                if col_idx < len(row):
                    original_value = row[col_idx]
                    if original_value is None or original_value == "":
                        continue

                    if not isinstance(original_value, str):
                        original_value = str(original_value)

                    if original_value:
                        # Xử lý giá trị phần trăm
                        cleaned_value = original_value.replace("%", "").strip()
                        try:
                            # Nếu là số, định dạng lại thành số nguyên + %
                            num_value = float(cleaned_value)
                            if 0 <= num_value <= 1:  # Nếu là số thập phân nhỏ (0.31)
                                new_value = f"{int(num_value * 100)}%"
                            else:  # Nếu đã là số nguyên (31)
                                new_value = f"{int(num_value)}%"

                            if new_value != original_value:
                                row[col_idx] = new_value
                                changes_count += 1
                        except ValueError:
                            # Nếu không phải số, giữ nguyên
                            pass

        return changes_count

    def _identify_empty_rows(self, values, column_indices):
        """Xác định các dòng trống dựa trên các cột đã chọn"""
        empty_rows = []
        for row_idx, row in enumerate(values):
            is_empty = True
            for col_idx in column_indices:
                if col_idx < len(row) and row[col_idx]:
                    is_empty = False
                    break

            if is_empty:
                empty_rows.append(row_idx)

        return empty_rows

    def process_imported_data_with_rules(self, spreadsheet_id, sheet_title, rules_to_apply):
        """
        Bước 2: Xử lý dữ liệu sau khi đã import với rules tùy chỉnh

        Args:
            spreadsheet_id: ID của spreadsheet đích
            sheet_title: Tên sheet đích đã import dữ liệu
            rules_to_apply: Danh sách các rules cần áp dụng
                           Mỗi rule có format: {'condition': 'tên điều kiện', 'headers': ['header1', 'header2']}

        Returns:
            True nếu thành công, False nếu thất bại
        """
        try:
            print(f"Bắt đầu xử lý dữ liệu trên sheet '{sheet_title}' với {len(rules_to_apply)} rules tùy chỉnh")

            # Lấy header của sheet đích (dòng 3)
            range_name = f"{sheet_title}!A3:ZZ3"
            result = self.sheets_service.spreadsheets().values().get(
                spreadsheetId=spreadsheet_id,
                range=range_name
            ).execute()
            headers = result.get('values', [[]])[0]

            if not headers:
                print("Không tìm thấy headers trong sheet đích")
                return False

            print(f"Đã tải {len(headers)} headers từ sheet")

            # Lấy toàn bộ dữ liệu trong sheet
            last_row = self.find_last_data_row(spreadsheet_id, sheet_title)
            if last_row <= 3:  # Nếu chỉ có dòng header hoặc ít dữ liệu hơn
                print("Không đủ dữ liệu để xử lý")
                return False

            range_name = f"{sheet_title}!A4:ZZ{last_row}"
            result = self.sheets_service.spreadsheets().values().get(
                spreadsheetId=spreadsheet_id,
                range=range_name,
                valueRenderOption="UNFORMATTED_VALUE"
            ).execute()

            values = result.get('values', [])
            if not values:
                print("Không có dữ liệu để xử lý")
                return False

            print(f"Đã lấy {len(values)} dòng dữ liệu để xử lý")

            # Biến đếm số lượng thay đổi
            total_changes = 0

            # Áp dụng từng rule
            for rule_idx, rule in enumerate(rules_to_apply):
                condition = rule['condition']
                selected_headers = rule['headers']

                print(f"Áp dụng rule {rule_idx + 1}: {condition} cho {len(selected_headers)} headers")

                # Tìm chỉ số cột tương ứng với headers đã chọn
                column_indices = []
                for header_name in selected_headers:
                    if header_name in headers:
                        col_idx = headers.index(header_name)
                        column_indices.append(col_idx)
                        print(f"  - Header '{header_name}' -> cột {col_idx}")
                    else:
                        print(f"  - Cảnh báo: Header '{header_name}' không tìm thấy trong sheet")

                if not column_indices:
                    print(f"  - Bỏ qua rule này vì không tìm thấy headers phù hợp")
                    continue

                # Áp dụng điều kiện tương ứng
                changes = 0
                if condition == "Clear 'X' Values":
                    changes = self._process_x_values(values, column_indices)
                elif condition == "Format Numbers":
                    # Gộp Format Numbers và Convert 'k' to '000'
                    changes1 = self._process_number_format(values, column_indices)
                    changes2 = self._process_k_values(values, column_indices)
                    changes = changes1 + changes2
                    print(f"    - Định dạng số: {changes1} thay đổi")
                    print(f"    - Chuyển đổi 'k' thành '000': {changes2} thay đổi")
                elif condition == "Format Percentages":
                    changes = self._process_percentages(values, column_indices)
                elif condition == "Fix Empty Rows":
                    # Gộp Fix Line Breaks và Clear Empty Rows
                    changes1 = self._process_line_breaks(values, column_indices)
                    changes2 = self._process_clear_empty_rows(values)
                    changes = changes1 + changes2
                    print(f"    - Sửa xuống dòng: {changes1} thay đổi")
                    print(f"    - Xóa dòng trống: {changes2} thay đổi")
                else:
                    print(f"  - Cảnh báo: Điều kiện '{condition}' không được hỗ trợ")
                    continue

                total_changes += changes
                print(f"  - Đã thực hiện {changes} thay đổi")

            # Cập nhật dữ liệu vào sheet nếu có thay đổi
            if total_changes > 0:
                print(f"Cập nhật {total_changes} thay đổi vào sheet...")

                # Cập nhật dữ liệu theo batch
                success = self.update_sheet_in_batches(
                    spreadsheet_id,
                    sheet_title,
                    values,
                    start_row=4,  # Bắt đầu từ dòng 4 (sau header ở dòng 3)
                    batch_size=50
                )

                if success:
                    print(f"Đã cập nhật thành công {total_changes} thay đổi vào sheet '{sheet_title}'")
                    return True
                else:
                    print("Có lỗi khi cập nhật dữ liệu vào sheet")
                    return False
            else:
                print("Không có thay đổi nào được thực hiện")
                return True

        except Exception as e:
            print(f"Lỗi khi xử lý dữ liệu với rules tùy chỉnh: {str(e)}")
            return False

    def _process_clear_empty_rows(self, values):
        """
        Xóa các dòng trống (tất cả các ô đều trống hoặc chỉ chứa khoảng trắng)

        Args:
            values: Dữ liệu sheet (list of lists)

        Returns:
            Số lượng dòng đã xóa
        """
        original_count = len(values)

        # Lọc ra các dòng không trống
        non_empty_rows = []
        for row in values:
            # Kiểm tra xem dòng có ít nhất một ô không trống không
            has_data = any(str(cell).strip() for cell in row if cell is not None)
            if has_data:
                non_empty_rows.append(row)

        # Cập nhật lại values
        values.clear()
        values.extend(non_empty_rows)

        removed_count = original_count - len(values)
        return removed_count

# Hàm chạy ví dụ
def run_example():
    raw_data = RawDataCore()

    # ID của spreadsheet đích
    target_spreadsheet_id = "YOUR_TARGET_SPREADSHEET_ID"

    # Danh sách các nguồn dữ liệu
    source_data = [
        {
            'spreadsheet_id': 'SOURCE_SPREADSHEET_ID_1',
            'sheet_name': 'Sheet1',
            'start_row': 3,  # Bắt đầu từ dòng 3 (bỏ qua header ở dòng 2)
            'end_row': None  # Lấy đến hết
        },
        {
            'spreadsheet_id': 'SOURCE_SPREADSHEET_ID_2',
            'sheet_name': 'Sheet2',
            'start_row': 3,  # Bắt đầu từ dòng 3
            'end_row': 100  # Chỉ lấy đến dòng 100
        }
    ]

    # Mapping header (sau khi đã chuẩn hóa)
    header_mapping = {
        "sanpham": "Sản phẩm",  # Header "Sản phẩm" (đã chuẩn hóa thành "sanpham") sẽ map vào cột "Sản phẩm" trong sheet đích
        "soluong": "Số lượng",  # Header "Số lượng" (đã chuẩn hóa thành "soluong") sẽ map vào cột "Số lượng" trong sheet đích
        "dongia": "Đơn giá"     # Header "Đơn giá" (đã chuẩn hóa thành "dongia") sẽ map vào cột "Đơn giá" trong sheet đích
    }

    # Hàm chuẩn hóa header
    def normalize_text(text):
        import unicodedata
        import re
        if not text:
            return ""
        # Chuyển về Unicode NFD để tách dấu
        text = unicodedata.normalize('NFD', text)
        # Loại bỏ các ký tự dấu
        text = ''.join([c for c in text if not unicodedata.combining(c)])
        # Xóa khoảng trống và chuyển về lowercase
        text = re.sub(r'\s+', '', text).lower()
        return text

    # Thực hiện import với chuẩn hóa header
    result = raw_data.process_data_import(
        target_spreadsheet_id,
        source_data,
        "Pool Deal",  # Sử dụng sheet "Pool Deal" làm đích
        header_mapping,
        normalize_text
    )

    if result:
        print("Import dữ liệu thành công!")
    else:
        print("Import dữ liệu thất bại!")

# Chạy trực tiếp
if __name__ == "__main__":
    run_example()