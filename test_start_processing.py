#!/usr/bin/env python3
"""
Test script để kiểm tra start_processing method
"""

import sys
import os
import time
from PyQt6.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QLabel, QTextEdit
from PyQt6.QtCore import QTimer

# Thêm đường dẫn để import
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data_scrape_ui import MainWindow

class TestStartProcessingWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Test Start Processing")
        self.setGeometry(100, 100, 600, 400)
        
        # Setup UI
        self.setup_ui()
        
        # Tạo MainWindow instance để test
        self.main_window = MainWindow()
        
    def setup_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Info label
        info_label = QLabel("Test Start Processing Method")
        info_label.setStyleSheet("font-size: 16px; font-weight: bold; margin: 10px;")
        layout.addWidget(info_label)
        
        # Status label
        self.status_label = QLabel("Status: Ready")
        layout.addWidget(self.status_label)
        
        # Log area
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        layout.addWidget(self.log_text)
        
        # Buttons
        test_btn = QPushButton("Test Start Processing")
        test_btn.clicked.connect(self.test_start_processing)
        layout.addWidget(test_btn)
        
        check_attrs_btn = QPushButton("Check Attributes")
        check_attrs_btn.clicked.connect(self.check_attributes)
        layout.addWidget(check_attrs_btn)
        
        show_main_btn = QPushButton("Show Main Window")
        show_main_btn.clicked.connect(self.show_main_window)
        layout.addWidget(show_main_btn)
        
    def log(self, message):
        """Add message to log"""
        timestamp = time.strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")
        
    def test_start_processing(self):
        """Test start_processing method"""
        self.log("Testing start_processing method...")
        
        try:
            # Check if main_window has required attributes
            if not hasattr(self.main_window, 'username'):
                self.log("❌ ERROR: main_window.username not found")
                return
                
            if not hasattr(self.main_window, 'password'):
                self.log("❌ ERROR: main_window.password not found")
                return
                
            self.log(f"✅ Username: {self.main_window.username}")
            self.log(f"✅ Password: {'*' * len(self.main_window.password)}")
            
            # Test with mock data
            self.main_window.id_edit.setPlainText("1. 123456789\n2. 987654321")
            self.main_window.output_path_edit.setText(os.path.expanduser("~/Desktop"))
            self.main_window.filename_edit.setText("test_output.xlsx")
            
            self.log("✅ Mock data set successfully")
            self.log("🚀 Ready to call start_processing()")
            
            # Note: We don't actually call start_processing() to avoid real scraping
            self.log("✅ Test completed successfully!")
            
        except Exception as e:
            self.log(f"❌ ERROR: {str(e)}")
            
    def check_attributes(self):
        """Check all important attributes"""
        self.log("Checking MainWindow attributes...")
        
        attrs_to_check = [
            'username', 'password', 'scraping_state', 'timer', 'total_products_count',
            'id_edit', 'output_path_edit', 'filename_edit', 'process_btn'
        ]
        
        for attr in attrs_to_check:
            if hasattr(self.main_window, attr):
                value = getattr(self.main_window, attr)
                if attr in ['username', 'password']:
                    display_value = value if attr == 'username' else '*' * len(value)
                    self.log(f"✅ {attr}: {display_value}")
                else:
                    self.log(f"✅ {attr}: {type(value).__name__}")
            else:
                self.log(f"❌ {attr}: NOT FOUND")
                
    def show_main_window(self):
        """Show the actual main window"""
        self.log("Showing main window...")
        self.main_window.show()

def main():
    app = QApplication(sys.argv)
    window = TestStartProcessingWindow()
    window.show()
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
