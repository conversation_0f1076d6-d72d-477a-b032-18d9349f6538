import sys
import os
import time
import re
import shutil
import datetime
from enum import Enum
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QLineEdit, QPushButton, QFileDialog, QTextEdit,
    QStackedWidget, QMessageBox, QProgressBar, QTableView,
    QHeaderView, QTabWidget, QSplitter, QFrame, QDialog, QToolButton, QMenu,
    QFormLayout, QDialogButtonBox
)
from PyQt6.QtCore import QThread, pyqtSignal, QTimer, Qt, QAbstractTableModel, QModelIndex, QEvent
from PyQt6.QtGui import QColor, QBrush, QFont, QKeyEvent
import data_scrape_core as core

# Thông tin đăng nhập mặc định
DEFAULT_USERNAME = "princekiix"
DEFAULT_PASSWORD = "Beyondk@2025"

# Đường dẫn lưu thông tin đăng nhập
APP_DATA_DIR = os.path.join(os.getenv('LOCALAPPDATA'), 'Data All in One', 'AutoShopee Scraping')
LOGIN_INFO_FILE = os.path.join(APP_DATA_DIR, 'login_info.txt')

# Cấu hình backup CSV
BACKUP_DIR = os.path.join(APP_DATA_DIR, 'csv_backups')
BACKUP_RETENTION_DAYS = 7

# Cấu hình số luồng tối ưu - Strategy 2 Stable Version
OPTIMAL_THREADS = 8

# Enum cho trạng thái scraping
class ScrapingState(Enum):
    IDLE = "idle"
    RUNNING = "running"
    PAUSED = "paused"
    STOPPING = "stopping"
    COMPLETED = "completed"
    ERROR = "error"

# Constants cho messages và cấu hình
class ScrapingMessages:
    BACKGROUND_CONTINUE = "Tiến trình scraping đang chạy trong nền và sẽ tiếp tục cho đến khi hoàn thành."
    CONFIRM_STOP_TITLE = "Xác nhận dừng tiến trình"
    CONFIRM_STOP_MESSAGE = "Có tiến trình scraping đang chạy.\n\nBạn có muốn dừng tiến trình này không?"
    CONFIRM_STOP_DETAILS = "• Tiến trình: {process_name}\n• Trạng thái: {status}\n• Thời gian chạy: {duration}\n• Tiến độ: {progress}"
    STOP_SUCCESS = "Đã dừng tiến trình scraping thành công."
    STOP_FAILED = "Không thể dừng tiến trình scraping. Vui lòng thử lại."

class ScrapingConfig:
    SHUTDOWN_TIMEOUT = 10.0  # Timeout cho việc shutdown threads (giây)
    CONFIRMATION_TIMEOUT = 30.0  # Timeout cho dialog xác nhận (giây)

# Trạng thái xử lý
STATUS_WAITING = "⏳ Chờ xử lý"
STATUS_PROCESSING = "🔄 Đang xử lý"
STATUS_COMPLETED = "✅ Hoàn thành"
STATUS_ERROR = "❌ Lỗi"
STATUS_NO_PRODUCTS = "ℹ️ Shop không có dữ liệu"

def setup_backup_system():
    """Tạo thư mục backup và cleanup cũ"""
    if not os.path.exists(BACKUP_DIR):
        os.makedirs(BACKUP_DIR)
    cleanup_old_backups()

def cleanup_old_backups():
    """Xóa backup files cũ hơn 7 ngày"""
    if not os.path.exists(BACKUP_DIR):
        return

    cutoff_time = time.time() - (BACKUP_RETENTION_DAYS * 24 * 3600)

    for filename in os.listdir(BACKUP_DIR):
        if filename.endswith('.csv'):
            filepath = os.path.join(BACKUP_DIR, filename)
            try:
                if os.path.getctime(filepath) < cutoff_time:
                    os.remove(filepath)
            except Exception:
                pass  # Ignore errors during cleanup

def create_backup_filename(original_path):
    """Tạo tên file backup với timestamp"""
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    basename = os.path.basename(original_path)
    name, ext = os.path.splitext(basename)
    return os.path.join(BACKUP_DIR, f"{name}_backup_{timestamp}{ext}")

class ScrapingStateManager:
    """Quản lý trạng thái scraping toàn cục"""

    def __init__(self):
        self.state = ScrapingState.IDLE
        self.worker_thread = None
        self.csv_thread = None
        self.start_time = None
        self.process_name = ""
        self.total_shops = 0
        self.completed_shops = 0

    def set_state(self, state: ScrapingState):
        """Cập nhật trạng thái scraping"""
        self.state = state

    def is_running(self) -> bool:
        """Kiểm tra xem có tiến trình nào đang chạy không"""
        return self.state in [ScrapingState.RUNNING, ScrapingState.STOPPING]

    def is_active(self) -> bool:
        """Kiểm tra xem có thread nào đang active không"""
        worker_active = self.worker_thread and self.worker_thread.isRunning()
        csv_active = self.csv_thread and self.csv_thread.isRunning()
        return worker_active or csv_active

    def start_scraping(self, worker_thread, process_name: str, total_shops: int):
        """Bắt đầu quá trình scraping"""
        self.worker_thread = worker_thread
        self.process_name = process_name
        self.total_shops = total_shops
        self.completed_shops = 0
        self.start_time = time.time()
        self.set_state(ScrapingState.RUNNING)

    def start_csv_conversion(self, csv_thread):
        """Bắt đầu quá trình convert CSV"""
        self.csv_thread = csv_thread

    def update_progress(self, completed_shops: int):
        """Cập nhật tiến độ"""
        self.completed_shops = completed_shops

    def get_duration(self) -> str:
        """Lấy thời gian đã chạy"""
        if not self.start_time:
            return "0:00"

        duration = time.time() - self.start_time
        minutes = int(duration // 60)
        seconds = int(duration % 60)
        return f"{minutes}:{seconds:02d}"

    def get_progress_text(self) -> str:
        """Lấy text hiển thị tiến độ"""
        if self.total_shops == 0:
            return "0%"

        percentage = (self.completed_shops / self.total_shops) * 100
        return f"{self.completed_shops}/{self.total_shops} ({percentage:.1f}%)"

    def stop_all_threads(self, timeout: float = ScrapingConfig.SHUTDOWN_TIMEOUT) -> bool:
        """Dừng tất cả threads một cách an toàn"""
        success = True

        # Dừng worker thread
        if self.worker_thread and self.worker_thread.isRunning():
            try:
                self.worker_thread.terminate()
                if not self.worker_thread.wait(int(timeout * 1000)):  # Convert to ms
                    success = False
            except Exception:
                success = False

        # Dừng CSV thread
        if self.csv_thread and self.csv_thread.isRunning():
            try:
                self.csv_thread.terminate()
                if not self.csv_thread.wait(int(timeout * 1000)):  # Convert to ms
                    success = False
            except Exception:
                success = False

        if success:
            self.set_state(ScrapingState.COMPLETED)
        else:
            self.set_state(ScrapingState.ERROR)

        return success

    def reset(self):
        """Reset trạng thái về IDLE"""
        self.state = ScrapingState.IDLE
        self.worker_thread = None
        self.csv_thread = None
        self.start_time = None
        self.process_name = ""
        self.total_shops = 0
        self.completed_shops = 0

class LoginDialog(QDialog):
    """Dialog để chỉnh sửa thông tin đăng nhập"""
    def __init__(self, current_username, current_password, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Thông tin đăng nhập")
        self.setModal(True)
        self.setFixedSize(400, 200)

        # Lưu thông tin hiện tại
        self.username = current_username
        self.password = current_password

        self.setup_ui()

    def setup_ui(self):
        layout = QVBoxLayout(self)
        layout.setSpacing(15)

        # Font lớn hơn
        big_font = QFont()
        big_font.setPointSize(11)

        # Username
        username_layout = QHBoxLayout()
        username_label = QLabel("Tên đăng nhập:")
        username_label.setFont(big_font)
        username_label.setFixedWidth(120)
        username_layout.addWidget(username_label)

        self.username_edit = QLineEdit()
        self.username_edit.setText(self.username)
        self.username_edit.setFont(big_font)
        self.username_edit.setMinimumHeight(30)
        username_layout.addWidget(self.username_edit)
        layout.addLayout(username_layout)

        # Password
        password_layout = QHBoxLayout()
        password_label = QLabel("Mật khẩu:")
        password_label.setFont(big_font)
        password_label.setFixedWidth(120)
        password_layout.addWidget(password_label)

        self.password_edit = QLineEdit()
        self.password_edit.setText(self.password)
        self.password_edit.setFont(big_font)
        self.password_edit.setMinimumHeight(30)
        self.password_edit.setEchoMode(QLineEdit.EchoMode.Password)
        password_layout.addWidget(self.password_edit)
        layout.addLayout(password_layout)

        # Buttons
        btn_layout = QHBoxLayout()

        save_btn = QPushButton("💾 Lưu")
        save_btn.setFont(big_font)
        save_btn.setMinimumHeight(35)
        save_btn.clicked.connect(self.save_and_close)

        cancel_btn = QPushButton("❌ Hủy")
        cancel_btn.setFont(big_font)
        cancel_btn.setMinimumHeight(35)
        cancel_btn.clicked.connect(self.reject)

        btn_layout.addStretch()
        btn_layout.addWidget(save_btn)
        btn_layout.addWidget(cancel_btn)
        btn_layout.addStretch()

        layout.addLayout(btn_layout)

    def save_and_close(self):
        """Lưu thông tin và đóng dialog"""
        self.username = self.username_edit.text().strip()
        self.password = self.password_edit.text().strip()

        if not self.username or not self.password:
            QMessageBox.warning(self, "Cảnh báo", "Vui lòng nhập đầy đủ thông tin đăng nhập!")
            return

        self.accept()

    def get_credentials(self):
        """Trả về thông tin đăng nhập"""
        return self.username, self.password

class ShopIdTextEdit(QTextEdit):
    """QTextEdit tùy chỉnh để xử lý việc dán ID shop với định dạng chuẩn"""
    def keyPressEvent(self, event: QKeyEvent) -> None:
        # Bắt sự kiện Ctrl+V
        if event.key() == Qt.Key.Key_V and event.modifiers() == Qt.KeyboardModifier.ControlModifier:
            clipboard = QApplication.clipboard()
            text = clipboard.text()

            # Xử lý và định dạng ID shop
            self.processAndFormatIds(text)
        else:
            super().keyPressEvent(event)

    def processAndFormatIds(self, text):
        # Tách theo dòng và loại bỏ khoảng trắng
        lines = [line.strip() for line in text.split('\n') if line.strip()]

        # Loại bỏ trùng lặp
        unique_lines = []
        seen = set()
        for line in lines:
            if line and line not in seen:
                seen.add(line)
                unique_lines.append(line)

        # Định dạng với số thứ tự
        formatted_text = "\n".join([f"{i+1}. {line}" for i, line in enumerate(unique_lines)])

        # Đặt vào text edit
        self.setText(formatted_text)

class ShopProgressTableModel(QAbstractTableModel):
    """Model cho bảng tiến trình shop"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.headers = ["STT", "ID Shop", "Trạng thái", "Số sản phẩm"]
        self.rows = []  # Dữ liệu: [(STT, ID, trạng_thái, số_sp)]
        self.shop_id_to_row = {}  # Map shop_id -> row_index

    def rowCount(self, parent=None):
        return len(self.rows)

    def columnCount(self, parent=None):
        return len(self.headers)

    def data(self, index, role=Qt.ItemDataRole.DisplayRole):
        if not index.isValid() or index.row() >= len(self.rows):
            return None

        row, col = index.row(), index.column()

        # Hiển thị dữ liệu
        if role == Qt.ItemDataRole.DisplayRole:
            return str(self.rows[row][col])

        # Căn lề
        elif role == Qt.ItemDataRole.TextAlignmentRole:
            if col == 0:  # STT - căn giữa
                return Qt.AlignmentFlag.AlignCenter
            elif col == 1:  # ID Shop - căn trái
                return Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter
            elif col == 2:  # Trạng thái - căn giữa
                return Qt.AlignmentFlag.AlignCenter
            elif col == 3:  # Số sản phẩm - căn phải
                return Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter

        return None

    def headerData(self, section, orientation, role=Qt.ItemDataRole.DisplayRole):
        if orientation == Qt.Orientation.Horizontal and role == Qt.ItemDataRole.DisplayRole:
            return self.headers[section]
        elif orientation == Qt.Orientation.Horizontal and role == Qt.ItemDataRole.FontRole:
            # Chỉ in đậm header
            font = QFont()
            font.setBold(True)
            return font
        return None

    def flags(self, index):
        # Không cho phép chỉnh sửa
        return Qt.ItemFlag.ItemIsEnabled | Qt.ItemFlag.ItemIsSelectable

    def addShop(self, shop_id, status=STATUS_WAITING):
        """Thêm shop mới vào bảng"""
        row_index = len(self.rows)
        self.beginInsertRows(QModelIndex(), row_index, row_index)
        self.rows.append([row_index + 1, shop_id, status, "0"])
        self.shop_id_to_row[shop_id] = row_index
        self.endInsertRows()

    def updateShopStatus(self, shop_id, status, product_count=0):
        """Cập nhật trạng thái và số sản phẩm của shop"""
        if shop_id in self.shop_id_to_row:
            row = self.shop_id_to_row[shop_id]
            self.rows[row][2] = status
            if product_count > 0:
                self.rows[row][3] = str(product_count)
            # Thông báo dữ liệu đã thay đổi
            self.dataChanged.emit(
                self.index(row, 0),
                self.index(row, 3)
            )

    def clearData(self):
        """Xóa toàn bộ dữ liệu trong bảng"""
        self.beginResetModel()
        self.rows = []
        self.shop_id_to_row = {}
        self.endResetModel()

class LoginThread(QThread):
    """Thread để đăng nhập vào Autoshopee khi khởi động"""
    login_completed = pyqtSignal(bool, str)

    def __init__(self, username, password):
        super().__init__()
        self.username = username
        self.password = password

    def run(self):
        try:
            # Gọi hàm đăng nhập từ core để lấy cookies mà không cần đợi
            core.get_logged_in_cookies(self.username, self.password, headless=True)
            # Kiểm tra xem headers có được tạo không
            headers = core.get_auth_headers()
            if headers:
                self.login_completed.emit(True, "Đăng nhập thành công")
            else:
                self.login_completed.emit(False, "Đăng nhập thất bại: Không nhận được headers")
        except Exception as e:
            self.login_completed.emit(False, f"Đăng nhập thất bại: {str(e)}")

class WorkerThread(QThread):
    """Thread xử lý dữ liệu để không block UI"""
    update_log = pyqtSignal(str)
    update_shop_status = pyqtSignal(str, str, int)  # shop_id, status, product_count
    finished = pyqtSignal(bool, str)
    update_completed = pyqtSignal(int, int)  # completed_count, total_count
    update_total_products = pyqtSignal(int)  # total_products_count

    def __init__(self, username, password, shop_ids, output_path, output_filename):
        super().__init__()
        self.username = username
        self.password = password
        self.shop_ids = shop_ids
        self.output_path = output_path
        self.output_filename = output_filename
        self.processed_count = 0
        self.completed_count = 0
        self.total_count = len(shop_ids)
        self.start_time = None
        self.total_products = 0

        # Tracking shop status để đảm bảo cleanup
        self.shop_status_tracker = {}

        # Khởi tạo tất cả ID shop với trạng thái chờ xử lý
        for shop_id in shop_ids:
            self.shop_status_tracker[shop_id] = STATUS_WAITING
            self.update_shop_status.emit(shop_id, STATUS_WAITING, 0)

    def log_callback(self, message):
        # Phân tích log message để xác định shop_id và cập nhật trạng thái
        shop_id_match = re.search(r"Đang lấy dữ liệu từ shop ID: (\d+)", message)
        completed_match = re.search(r"Shop ID (\d+): Hoàn thành với (\d+) sản phẩm", message)
        no_products_match = re.search(r"Shop ID (\d+): Không có sản phẩm nào", message)
        no_products_retry_match = re.search(r"Shop ID (\d+): Không có sản phẩm nào \(đã retry (\d+) lần\)", message)
        error_match = re.search(r"Lỗi khi xử lý shop ID (\d+)", message)
        retry_match = re.search(r"Retry lần (\d+) cho shop ID: (\d+)", message)

        # Cập nhật trạng thái theo shop ID
        if shop_id_match:
            shop_id = shop_id_match.group(1)
            self.shop_status_tracker[shop_id] = STATUS_PROCESSING
            self.update_shop_status.emit(shop_id, STATUS_PROCESSING, 0)
            self.processed_count += 1
        elif retry_match:
            # Retry message - chỉ cập nhật trạng thái, KHÔNG đếm
            retry_count = retry_match.group(1)
            shop_id = retry_match.group(2)
            self.shop_status_tracker[shop_id] = STATUS_PROCESSING
            self.update_shop_status.emit(shop_id, f"🔄 Retry {retry_count}", 0)
        elif completed_match:
            # Hoàn thành với sản phẩm - ĐẾM
            shop_id = completed_match.group(1)
            product_count = int(completed_match.group(2))
            self.shop_status_tracker[shop_id] = STATUS_COMPLETED
            self.update_shop_status.emit(shop_id, STATUS_COMPLETED, product_count)
            self.completed_count += 1
            # Cập nhật tổng số sản phẩm
            self.total_products += product_count
            self.update_total_products.emit(self.total_products)
            # Phát tín hiệu cập nhật tiến trình dựa trên shop đã hoàn thành
            self.update_completed.emit(self.completed_count, self.total_count)
        elif no_products_retry_match:
            # Không có sản phẩm sau retry - ĐẾM (kết quả cuối cùng)
            shop_id = no_products_retry_match.group(1)
            retry_count = no_products_retry_match.group(2)
            self.shop_status_tracker[shop_id] = STATUS_NO_PRODUCTS
            self.update_shop_status.emit(shop_id, f"ℹ️ 0 SP (retry {retry_count}x)", 0)
            self.completed_count += 1
            # Cập nhật tiến trình (shop đã được xử lý xong)
            self.update_completed.emit(self.completed_count, self.total_count)
        elif no_products_match:
            # Không có sản phẩm (không retry) - ĐẾM
            shop_id = no_products_match.group(1)
            self.shop_status_tracker[shop_id] = STATUS_NO_PRODUCTS
            self.update_shop_status.emit(shop_id, STATUS_NO_PRODUCTS, 0)
            self.completed_count += 1
            # Cập nhật tiến trình (shop đã được xử lý xong)
            self.update_completed.emit(self.completed_count, self.total_count)
        elif error_match:
            # Lỗi - ĐẾM
            shop_id = error_match.group(1)
            self.shop_status_tracker[shop_id] = STATUS_ERROR
            self.update_shop_status.emit(shop_id, STATUS_ERROR, 0)
            self.completed_count += 1
            # Cập nhật tiến trình ngay cả khi lỗi (vì shop đã được xử lý xong)
            self.update_completed.emit(self.completed_count, self.total_count)

        # Chỉ gửi log quan trọng lên tab log chi tiết (lọc bỏ các messages không cần thiết)
        if not any(skip_phrase in message for skip_phrase in [
            "⏳ Đợi", "📦 Trang", "Đợi", "giây trước", "request tiếp theo",
            "Response không phải JSON", "Đã thử lại tối đa số lần", "❌ Response không phải JSON",
            "❌ Đã thử lại tối đa số lần", "⚠️ Response rỗng", "❌ API lỗi", "❌ HTTP lỗi",
            "⏱️ Timeout khi gọi API", "🔌 Lỗi kết nối", "❌ Lỗi không xác định khi gọi API",
            "❌ Lỗi giải mã JSON", "📄 Response content"
        ]):
            self.update_log.emit(message)

    def cleanup_unfinished_shops(self):
        """Cleanup các shop chưa hoàn thành - đảm bảo không có shop nào bị treo"""
        unfinished_shops = []
        for shop_id, status in self.shop_status_tracker.items():
            if status in [STATUS_WAITING, STATUS_PROCESSING]:
                unfinished_shops.append(shop_id)
                # Đánh dấu shop bị timeout/lỗi
                self.shop_status_tracker[shop_id] = STATUS_ERROR
                self.update_shop_status.emit(shop_id, STATUS_ERROR, 0)
                self.completed_count += 1

        if unfinished_shops:
            self.update_log.emit(f"⚠️ Cleanup {len(unfinished_shops)} shop chưa hoàn thành: {', '.join(unfinished_shops)}")
            # Cập nhật tiến trình cuối cùng
            self.update_completed.emit(self.completed_count, self.total_count)

    def run(self):
        try:
            self.start_time = time.time()
            self.update_log.emit("🚀 Bắt đầu quá trình xử lý dữ liệu...")

            # Đảm bảo thư mục đầu ra tồn tại
            if not os.path.exists(self.output_path):
                os.makedirs(self.output_path)

            # Tạo đường dẫn đầy đủ cho file Excel
            full_path = os.path.join(self.output_path, self.output_filename)
            if not full_path.endswith('.xlsx'):
                full_path += '.xlsx'

            # Gọi hàm xử lý từ core với số luồng cố định tối ưu
            result = core.fetch_and_save_multiple_shops(
                self.username,
                self.password,
                self.shop_ids,
                output_filename=full_path,
                headless=True,  # Luôn chạy ở chế độ headless
                timeout=30,
                max_retries=3,
                log_callback=self.log_callback,
                max_workers=OPTIMAL_THREADS
            )

            if result:
                self.update_log.emit(f"📊 Tổng cộng: {self.total_products} sản phẩm được lưu vào file Excel")
                self.finished.emit(True, full_path)
            else:
                self.finished.emit(False, "")

        except Exception as e:
            self.update_log.emit(f"❌ Lỗi: {str(e)}")
            self.finished.emit(False, str(e))
        finally:
            # Cleanup các shop chưa hoàn thành để đảm bảo không có shop nào bị treo
            self.cleanup_unfinished_shops()

            # Tính thời gian xử lý
            if self.start_time:
                elapsed_time = time.time() - self.start_time
                hours, remainder = divmod(elapsed_time, 3600)
                minutes, seconds = divmod(remainder, 60)
                if hours > 0:
                    self.update_log.emit(f"⏱️ Thời gian xử lý: {int(hours)} giờ {int(minutes)} phút {int(seconds)} giây")
                else:
                    self.update_log.emit(f"⏱️ Thời gian xử lý: {int(minutes)} phút {int(seconds)} giây")

class CSVConversionThread(QThread):
    """Thread để convert CSV sang Excel với smart retry"""
    conversion_completed = pyqtSignal(bool, str, str)  # success, excel_path, error_msg
    conversion_progress = pyqtSignal(str)  # progress message

    def __init__(self, csv_path, excel_path):
        super().__init__()
        self.csv_path = csv_path
        self.excel_path = excel_path

    def run(self):
        try:
            self.conversion_progress.emit("🔄 Bắt đầu conversion CSV → Excel...")

            # Tạo backup CSV
            backup_path = create_backup_filename(self.csv_path)
            shutil.copy2(self.csv_path, backup_path)
            self.conversion_progress.emit(f"💾 Đã tạo backup: {os.path.basename(backup_path)}")

            # Thử convert với smart retry
            success = self.convert_with_smart_retry()

            if success:
                self.conversion_progress.emit("✅ Conversion hoàn tất!")
                self.conversion_completed.emit(True, self.excel_path, "")
                # Xóa backup nếu thành công
                try:
                    os.remove(backup_path)
                except:
                    pass
            else:
                self.conversion_progress.emit("❌ Conversion thất bại!")
                self.conversion_completed.emit(False, "", f"Backup CSV: {backup_path}")

        except Exception as e:
            self.conversion_completed.emit(False, "", str(e))

    def convert_with_smart_retry(self, max_retries=3):
        """Convert với retry thông minh"""
        for attempt in range(max_retries):
            try:
                self.conversion_progress.emit(f"🔄 Thử conversion lần {attempt + 1}/{max_retries}")

                if attempt == 0:
                    # Method 1: Sử dụng core function (giữ nguyên format)
                    success = self.convert_using_core_format()
                elif attempt == 1:
                    # Method 2: Pandas simple conversion
                    success = self.convert_using_pandas()
                else:
                    # Method 3: Chunked processing
                    success = self.convert_chunked()

                if success:
                    return True

            except Exception as e:
                self.conversion_progress.emit(f"❌ Lần thử {attempt + 1} thất bại: {str(e)}")

        return False

    def convert_using_core_format(self):
        """Sử dụng format giống hệt core để convert"""
        try:
            import pandas as pd
            from openpyxl import Workbook
            from openpyxl.styles import Font

            # Đọc CSV
            df = pd.read_csv(self.csv_path)

            # Apply format giống hệt StreamingExcelWriter._format_dataframe()
            formatted_df = self.format_dataframe_like_core(df)

            # Tạo Excel với format giống hệt
            wb = Workbook()
            ws = wb.active

            # Write headers (3 cột đầu trống)
            for c_idx, column in enumerate(formatted_df.columns, 1):
                if c_idx <= 3:
                    header_value = ""
                else:
                    header_value = column
                ws.cell(row=1, column=c_idx, value=header_value)
                ws.cell(row=1, column=c_idx).font = Font(bold=False)

            # Write data từ row 2
            for r_idx, (_, row) in enumerate(formatted_df.iterrows(), 2):
                for c_idx, value in enumerate(row, 1):
                    try:
                        ws.cell(row=r_idx, column=c_idx, value=value)
                    except:
                        ws.cell(row=r_idx, column=c_idx, value="[Lỗi dữ liệu]")

            wb.save(self.excel_path)
            return True

        except Exception:
            return False

    def format_dataframe_like_core(self, df):
        """Format DataFrame giống hệt như core._format_dataframe()"""
        import pandas as pd

        # Tạo dữ liệu cho 3 cột đầu tiên
        key_col = df['id'].astype(str) + '_' + df['shopID'].astype(str)
        sku_col = df['id']
        id_col = df['id']

        # Tạo DataFrame với cấu trúc mới
        new_df = pd.DataFrame({
            '': key_col,  # Cột trống 1
            ' ': sku_col,  # Cột trống 2
            '  ': id_col   # Cột trống 3
        })

        # Thêm các cột còn lại (giống hệt core)
        new_df['Link sản phẩm'] = df['linkProduct'] if 'linkProduct' in df.columns else ''
        new_df['Link Shop'] = df['linkShop'] if 'linkShop' in df.columns else ''
        new_df['Tên sản phẩm'] = df['name'] if 'name' in df.columns else ''
        new_df['Thương hiệu'] = df['brand'] if 'brand' in df.columns else ''
        new_df['Mô tả'] = df['description'] if 'description' in df.columns else ''
        new_df['Ngày tạo'] = df['timeCreate'] if 'timeCreate' in df.columns else ''
        new_df['Mã Shop'] = df['itemID'] if 'itemID' in df.columns else ''
        new_df['Mã Sản phẩm'] = df['shopID'] if 'shopID' in df.columns else ''
        new_df['Chuyên mục'] = df['categoryMain'] if 'categoryMain' in df.columns else ''
        new_df['Giá'] = df['price'] if 'price' in df.columns else ''
        new_df['Giá min'] = df['priceMin'] if 'priceMin' in df.columns else ''
        new_df['Giá max'] = df['priceMax'] if 'priceMax' in df.columns else ''
        new_df['Giảm giá'] = df['discount'] if 'discount' in df.columns else ''
        new_df['Kho'] = df['stock'] if 'stock' in df.columns else ''
        new_df['Số thích'] = df['likedCount'] if 'likedCount' in df.columns else ''
        new_df['Điểm đánh giá'] = df['itemRating'] if 'itemRating' in df.columns else ''
        new_df['Đã bán 30 ngày'] = df['sold_30day'] if 'sold_30day' in df.columns else ''
        new_df['Doanh số 30 ngày'] = df['sale_30day'] if 'sale_30day' in df.columns else ''
        new_df['Đã bán toàn thời gian'] = df['sold_alltime'] if 'sold_alltime' in df.columns else ''
        new_df['Doanh số toàn thời gian'] = df['sale_alltime'] if 'sale_alltime' in df.columns else ''
        new_df['Vị trí'] = df['location'] if 'location' in df.columns else ''
        new_df['Video'] = df['video'] if 'video' in df.columns else ''

        return new_df

    def convert_using_pandas(self):
        """Fallback: Pandas simple conversion"""
        try:
            import pandas as pd
            df = pd.read_csv(self.csv_path)
            df.to_excel(self.excel_path, index=False)
            return True
        except Exception:
            return False

    def convert_chunked(self):
        """Fallback: Chunked processing cho file lớn"""
        try:
            import pandas as pd
            from openpyxl import Workbook

            chunk_size = 5000
            wb = Workbook()
            ws = wb.active
            current_row = 1
            headers_written = False

            for chunk in pd.read_csv(self.csv_path, chunksize=chunk_size):
                if not headers_written:
                    # Write headers
                    for c_idx, col in enumerate(chunk.columns, 1):
                        ws.cell(row=current_row, column=c_idx, value=col)
                    current_row += 1
                    headers_written = True

                # Write data
                for _, row in chunk.iterrows():
                    for c_idx, value in enumerate(row, 1):
                        ws.cell(row=current_row, column=c_idx, value=value)
                    current_row += 1

            wb.save(self.excel_path)
            return True
        except Exception:
            return False

class ConfirmationDialog(QDialog):
    """Dialog xác nhận dừng tiến trình scraping"""

    def __init__(self, state_manager: ScrapingStateManager, parent=None):
        super().__init__(parent)
        self.state_manager = state_manager
        self.setWindowTitle(ScrapingMessages.CONFIRM_STOP_TITLE)
        self.setModal(True)
        self.setFixedSize(500, 300)
        self.setup_ui()

    def setup_ui(self):
        layout = QVBoxLayout(self)
        layout.setSpacing(20)

        # Icon và title
        title_layout = QHBoxLayout()
        title_label = QLabel("⚠️ " + ScrapingMessages.CONFIRM_STOP_TITLE)
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #d32f2f;")
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        layout.addLayout(title_layout)

        # Message chính
        message_label = QLabel(ScrapingMessages.CONFIRM_STOP_MESSAGE)
        message_label.setWordWrap(True)
        message_label.setStyleSheet("font-size: 14px; margin: 10px 0;")
        layout.addWidget(message_label)

        # Chi tiết tiến trình
        details_text = ScrapingMessages.CONFIRM_STOP_DETAILS.format(
            process_name=self.state_manager.process_name or "Data Scraping",
            status=self.state_manager.state.value,
            duration=self.state_manager.get_duration(),
            progress=self.state_manager.get_progress_text()
        )

        details_label = QLabel(details_text)
        details_label.setWordWrap(True)
        details_label.setStyleSheet("""
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
        """)
        layout.addWidget(details_label)

        # Buttons
        button_layout = QHBoxLayout()

        # Nút Tiếp tục (không dừng)
        continue_btn = QPushButton("🔄 Tiếp tục chạy")
        continue_btn.setStyleSheet("""
            QPushButton {
                background-color: #4caf50;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        continue_btn.clicked.connect(self.accept)

        # Nút Dừng
        stop_btn = QPushButton("🛑 Dừng tiến trình")
        stop_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
        """)
        stop_btn.clicked.connect(self.reject)

        button_layout.addStretch()
        button_layout.addWidget(continue_btn)
        button_layout.addWidget(stop_btn)
        button_layout.addStretch()

        layout.addLayout(button_layout)

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("AutoShopee Data Scraper")
        self.setMinimumSize(900, 650)

        # Thêm các thuộc tính để tích hợp với main.py
        self.back_callback = None
        self._goto_program_callback = None

        # Khởi tạo ScrapingStateManager
        self.scraping_state = ScrapingStateManager()

        # Tạo stacked widget để chứa các bước
        self.stacked_widget = QStackedWidget()
        self.setCentralWidget(self.stacked_widget)

        # Tạo các widget cho từng bước
        self.setup_step1()
        self.setup_step2()

        # Hiển thị bước 1 đầu tiên
        self.stacked_widget.setCurrentIndex(0)

        # Khởi tạo timer cho việc hiển thị thời gian xử lý
        self.timer = QTimer(self)
        self.timer.timeout.connect(self.update_timer)
        self.start_time = None

        # Tổng số sản phẩm
        self.total_products_count = 0

        # Tạo thư mục lưu thông tin đăng nhập nếu chưa tồn tại
        if not os.path.exists(APP_DATA_DIR):
            os.makedirs(APP_DATA_DIR)

        setup_backup_system()

        # Tải thông tin đăng nhập nếu có
        self.load_login_info()

        # Tự động đăng nhập khi khởi động
        self.login_thread = LoginThread(DEFAULT_USERNAME, DEFAULT_PASSWORD)
        self.login_thread.login_completed.connect(self.on_login_completed)
        self.login_thread.start()

    def handle_back(self):
        """Xử lý khi người dùng nhấn nút Back với confirmation dialog"""
        # Kiểm tra xem có tiến trình scraping đang chạy không
        if self.scraping_state.is_active():
            # Hiển thị dialog xác nhận
            dialog = ConfirmationDialog(self.scraping_state, self)
            result = dialog.exec()

            if result == QDialog.DialogCode.Accepted:
                # Người dùng chọn tiếp tục - không dừng tiến trình
                pass
            else:
                # Người dùng chọn dừng tiến trình
                success = self.stop_scraping_safely()
                if success:
                    QMessageBox.information(self, "Thông báo", ScrapingMessages.STOP_SUCCESS)
                else:
                    QMessageBox.warning(self, "Cảnh báo", ScrapingMessages.STOP_FAILED)
                    return  # Không quay về nếu không dừng được

        # Gọi callback để quay về màn hình chính
        if self.back_callback:
            self.back_callback()

    def goto_other_program(self, program_name):
        """Chuyển đến chương trình khác với thông báo background processing"""
        # Kiểm tra xem có tiến trình scraping đang chạy không
        if self.scraping_state.is_active():
            # Hiển thị thông báo tiến trình sẽ tiếp tục chạy
            QMessageBox.information(
                self,
                "Thông báo",
                ScrapingMessages.BACKGROUND_CONTINUE
            )

        # Gọi callback để chuyển đến chương trình khác
        if self._goto_program_callback and callable(self._goto_program_callback):
            self._goto_program_callback(program_name)

    def stop_scraping_safely(self) -> bool:
        """Dừng tất cả các tiến trình scraping một cách an toàn"""
        try:
            # Cập nhật trạng thái đang dừng
            self.scraping_state.set_state(ScrapingState.STOPPING)

            # Sử dụng ScrapingStateManager để dừng threads
            success = self.scraping_state.stop_all_threads()

            # Dừng timer nếu đang chạy
            if hasattr(self, 'timer') and self.timer and self.timer.isActive():
                self.timer.stop()

            # Reset state manager
            if success:
                self.scraping_state.reset()

            return success

        except Exception as e:
            print(f"Lỗi khi dừng scraping: {e}")
            return False

    def stop_scraping(self):
        """Dừng tất cả các tiến trình scraping đang chạy (legacy method)"""
        # Dừng worker thread nếu đang chạy
        if hasattr(self, 'worker_thread') and self.worker_thread and self.worker_thread.isRunning():
            self.worker_thread.terminate()
            self.worker_thread.wait(1000)  # Đợi tối đa 1 giây
            print("Đã dừng worker thread")

        # Dừng CSV conversion thread nếu đang chạy
        if hasattr(self, 'csv_thread') and self.csv_thread and self.csv_thread.isRunning():
            self.csv_thread.terminate()
            self.csv_thread.wait(1000)  # Đợi tối đa 1 giây
            print("Đã dừng CSV conversion thread")

        # Dừng timer nếu đang chạy
        if hasattr(self, 'timer') and self.timer and self.timer.isActive():
            self.timer.stop()

        # Reset state manager
        self.scraping_state.reset()

        print("Đã dừng tất cả các tiến trình scraping")

    def on_login_completed(self, success, message):
        """Xử lý khi đăng nhập hoàn tất"""
        if success:
            # Enable nút xử lý dữ liệu khi đăng nhập thành công
            if hasattr(self, 'process_btn'):
                self.process_btn.setEnabled(True)
        else:
            # Vẫn disable nút và hiển thị thông báo lỗi
            if hasattr(self, 'process_btn'):
                self.process_btn.setEnabled(False)
            QMessageBox.warning(self, "Lỗi đăng nhập", f"Không thể đăng nhập: {message}")

    def setup_step1(self):
        """Thiết lập giao diện cho bước 1: Nhập thông tin và ID"""
        step1_widget = QWidget()
        layout = QVBoxLayout(step1_widget)
        layout.setSpacing(15)  # Tăng khoảng cách giữa các thành phần

        # Tạo top bar với nút Back và Context menu
        top_bar = QHBoxLayout()
        btn_back = QPushButton("⬅️ Trở về giao diện chính")
        btn_back.setFixedSize(150, 40)
        btn_back.setStyleSheet("border: none; font-size: 12px; font-weight: bold;")
        btn_back.clicked.connect(self.handle_back)
        top_bar.addWidget(btn_back, alignment=Qt.AlignmentFlag.AlignLeft)

        self.context_button = QToolButton()
        self.context_button.setText("Chuyển đến ➡️")
        self.context_button.setFixedSize(100, 30)
        self.context_button.setStyleSheet("""
            QToolButton {
                border: none;
                background: transparent;
                font-size: 12px;
                font-weight: bold;
            }
            QToolButton::menu-indicator {
                image: none;
                width: 0px;
            }
        """)
        self.context_button.installEventFilter(self)
        self.context_button.setPopupMode(QToolButton.ToolButtonPopupMode.InstantPopup)
        menu = QMenu(self.context_button)
        menu.addAction("Data Handler", lambda: self.goto_other_program("data_handler"))
        menu.addAction("Import Data", lambda: self.goto_other_program("import_data"))
        menu.addAction("Internal Data", lambda: self.goto_other_program("internal_data"))
        menu.addAction("Image Scraping", lambda: self.goto_other_program("image_scraping"))
        menu.addAction("Update Level Model", lambda: self.goto_other_program("external_update"))
        menu.addAction("AI Classification", lambda: self.goto_other_program("ai_classification"))
        self.context_button.setMenu(menu)
        top_bar.addWidget(self.context_button, alignment=Qt.AlignmentFlag.AlignRight)

        layout.addLayout(top_bar)
        layout.addSpacing(5)

        # Tạo font lớn hơn cho các label
        big_font = QFont()
        big_font.setPointSize(11)  # Tăng kích thước font

        # Dòng 1: Chọn nơi lưu file Excel + Convert CSV
        file_layout = QHBoxLayout()
        file_label = QLabel("Nơi lưu file:")
        file_label.setFont(big_font)
        file_layout.addWidget(file_label)

        self.output_path_edit = QLineEdit()
        self.output_path_edit.setText(os.path.expanduser("~\\Desktop"))  # Mặc định là Desktop
        self.output_path_edit.setMinimumHeight(30)  # Tăng chiều cao
        self.output_path_edit.setFont(big_font)
        file_layout.addWidget(self.output_path_edit)

        browse_btn = QPushButton("Browse")
        browse_btn.setMinimumHeight(30)  # Tăng chiều cao
        browse_btn.setFont(big_font)
        browse_btn.clicked.connect(self.browse_folder)
        file_layout.addWidget(browse_btn)

        # Nút Convert CSV - chuyển lên cùng dòng với chọn nơi lưu file
        self.convert_btn = QPushButton("🔄 Convert CSV")
        self.convert_btn.clicked.connect(self.manual_convert_csv)
        self.convert_btn.setMinimumHeight(30)
        self.convert_btn.setFont(big_font)
        self.convert_btn.setToolTip("Chuyển đổi file CSV thành Excel")
        file_layout.addWidget(self.convert_btn)

        layout.addLayout(file_layout)

        # Dòng 2: Nhập tên file + Thông tin đăng nhập
        filename_layout = QHBoxLayout()
        filename_label = QLabel("Tên file Excel:")
        filename_label.setFont(big_font)
        filename_layout.addWidget(filename_label)

        self.filename_edit = QLineEdit()
        self.filename_edit.setText(f"Scraped.xlsx")
        self.filename_edit.setMinimumHeight(30)  # Tăng chiều cao
        self.filename_edit.setFont(big_font)
        filename_layout.addWidget(self.filename_edit)

        # Nút Thông tin đăng nhập - cùng dòng với tên file
        self.login_info_btn = QPushButton("🔑 Thông tin đăng nhập")
        self.login_info_btn.clicked.connect(self.show_login_dialog)
        self.login_info_btn.setMinimumHeight(30)
        self.login_info_btn.setFont(big_font)
        self.login_info_btn.setToolTip("Chỉnh sửa thông tin đăng nhập")
        filename_layout.addWidget(self.login_info_btn)

        layout.addLayout(filename_layout)

        # Dòng 3: Nhập ID shop - Thay thế QTextEdit tiêu chuẩn bằng ShopIdTextEdit
        id_label = QLabel("Danh sách ID shop:")
        id_label.setFont(big_font)
        layout.addWidget(id_label)

        self.id_edit = ShopIdTextEdit()
        self.id_edit.setPlaceholderText("Nhập ID shop ở đây hoặc dán từ clipboard")
        self.id_edit.setFont(big_font)
        self.id_edit.setMinimumHeight(250)  # Tăng chiều cao ô nhập ID
        layout.addWidget(self.id_edit)

        # Dòng 4: Các nút chức năng (chỉ còn 2 nút)
        btn_layout = QHBoxLayout()
        paste_btn = QPushButton("Nhập từ Clipboard")
        paste_btn.clicked.connect(self.paste_from_clipboard)
        paste_btn.setFixedWidth(180)  # Tăng chiều rộng vì chỉ còn 2 nút
        paste_btn.setFixedHeight(50)  # Tăng chiều cao nút
        paste_btn.setFont(big_font)

        self.process_btn = QPushButton("Xử lý dữ liệu")
        self.process_btn.clicked.connect(self.start_processing)
        self.process_btn.setFixedWidth(180)  # Tăng chiều rộng vì chỉ còn 2 nút
        self.process_btn.setFixedHeight(50)  # Tăng chiều cao nút
        self.process_btn.setFont(big_font)
        self.process_btn.setEnabled(False)  # Mặc định disable cho đến khi đăng nhập thành công

        # Tạo layout riêng để nhóm các nút lại với nhau
        btn_group = QHBoxLayout()
        btn_group.addWidget(paste_btn)
        btn_group.addWidget(self.process_btn)
        btn_group.setSpacing(20)  # Khoảng cách giữa 2 nút

        btn_layout.addStretch()  # Khoảng trống bên trái
        btn_layout.addLayout(btn_group)  # Nhóm nút ở giữa
        btn_layout.addStretch()  # Khoảng trống bên phải

        layout.addLayout(btn_layout)
        layout.addSpacing(20)  # Thêm khoảng trống ở cuối

        # Thêm widget vào stacked widget
        self.stacked_widget.addWidget(step1_widget)

    def setup_step2(self):
        """Thiết lập giao diện cho bước 2: Hiển thị log và tiến trình xử lý"""
        step2_widget = QWidget()
        layout = QVBoxLayout(step2_widget)

        # Tạo top bar với nút Back và Context menu
        top_bar = QHBoxLayout()
        btn_back = QPushButton("⬅️ Trở về giao diện chính")
        btn_back.setFixedSize(150, 40)
        btn_back.setStyleSheet("border: none; font-size: 12px; font-weight: bold;")
        btn_back.clicked.connect(self.handle_back)
        top_bar.addWidget(btn_back, alignment=Qt.AlignmentFlag.AlignLeft)

        self.context_button_step2 = QToolButton()
        self.context_button_step2.setText("Chuyển đến ➡️")
        self.context_button_step2.setFixedSize(100, 30)
        self.context_button_step2.setStyleSheet("""
            QToolButton {
                border: none;
                background: transparent;
                font-size: 12px;
                font-weight: bold;
            }
            QToolButton::menu-indicator {
                image: none;
                width: 0px;
            }
        """)
        self.context_button_step2.installEventFilter(self)
        self.context_button_step2.setPopupMode(QToolButton.ToolButtonPopupMode.InstantPopup)
        menu = QMenu(self.context_button_step2)
        menu.addAction("Data Handler", lambda: self.goto_other_program("data_handler"))
        menu.addAction("Import Data", lambda: self.goto_other_program("import_data"))
        menu.addAction("Internal Data", lambda: self.goto_other_program("internal_data"))
        menu.addAction("Image Scraping", lambda: self.goto_other_program("image_scraping"))
        menu.addAction("Update Level Model", lambda: self.goto_other_program("external_update"))
        menu.addAction("AI Classification", lambda: self.goto_other_program("ai_classification"))
        self.context_button_step2.setMenu(menu)
        top_bar.addWidget(self.context_button_step2, alignment=Qt.AlignmentFlag.AlignRight)

        layout.addLayout(top_bar)
        layout.addSpacing(5)

        # Hiển thị thời gian xử lý - Dòng 1
        header_layout = QHBoxLayout()

        # Thiết lập căn lề thời gian bên trái và phần trăm bên phải
        self.timer_label = QLabel("⏱️ Thời gian: 00:00:00")
        self.timer_label.setAlignment(Qt.AlignmentFlag.AlignLeft)  # Căn trái cho thời gian
        big_font = QFont()
        big_font.setPointSize(11)
        self.timer_label.setFont(big_font)

        # Hiển thị tiến trình tổng thể
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)

        # Tạo label hiển thị phần trăm bên phải
        self.percent_label = QLabel("0%")
        self.percent_label.setAlignment(Qt.AlignmentFlag.AlignRight)  # Căn phải cho phần trăm
        self.percent_label.setFont(big_font)

        header_layout.addWidget(self.timer_label, 2)  # Tỷ lệ 2
        header_layout.addWidget(self.progress_bar, 6)  # Tỷ lệ 6
        header_layout.addWidget(self.percent_label, 2)  # Tỷ lệ 2

        layout.addLayout(header_layout)

        # Dòng 2: Hiển thị số shop đã hoàn thành
        shop_counter_layout = QHBoxLayout()
        shop_counter_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)  # Căn giữa

        self.shop_counter_label = QLabel("💻 Hoàn thành: 0/0")
        self.shop_counter_label.setAlignment(Qt.AlignmentFlag.AlignCenter)  # Căn giữa label

        # Tạo font in đậm và kích thước lớn hơn
        bold_font = QFont()
        bold_font.setBold(True)
        bold_font.setPointSize(12)  # Tăng kích thước font
        self.shop_counter_label.setFont(bold_font)

        shop_counter_layout.addWidget(self.shop_counter_label)

        layout.addLayout(shop_counter_layout)

        # Tạo widget tab để phân tách bảng tiến trình và log
        tab_widget = QTabWidget()

        # Tab 1: Bảng tiến trình cho từng shop
        progress_tab = QWidget()
        progress_layout = QVBoxLayout(progress_tab)

        # Tạo model và view cho bảng tiến trình - thay thế QTableWidget bằng QTableView
        self.progress_model = ShopProgressTableModel()
        self.progress_table = QTableView()
        self.progress_table.setModel(self.progress_model)
        self.progress_table.setSelectionBehavior(QTableView.SelectionBehavior.SelectRows)
        self.progress_table.setSelectionMode(QTableView.SelectionMode.SingleSelection)

        # Cấu hình header và cột
        header = self.progress_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)  # STT
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)  # ID Shop
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)  # Trạng thái
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)  # Số sản phẩm

        progress_layout.addWidget(self.progress_table)

        # Thêm ô hiển thị tổng số sản phẩm
        products_summary_layout = QHBoxLayout()

        # Thêm đường kẻ ngang phân cách
        line = QFrame()
        line.setFrameShape(QFrame.Shape.HLine)
        line.setFrameShadow(QFrame.Shadow.Sunken)
        progress_layout.addWidget(line)

        # Tạo label tổng số sản phẩm với font in đậm
        self.total_products_label = QLabel("📊 Tổng số sản phẩm: 0")
        bold_font = QFont()
        bold_font.setBold(True)
        bold_font.setPointSize(11)
        self.total_products_label.setFont(bold_font)
        self.total_products_label.setAlignment(Qt.AlignmentFlag.AlignRight)

        products_summary_layout.addStretch()
        products_summary_layout.addWidget(self.total_products_label)

        progress_layout.addLayout(products_summary_layout)

        tab_widget.addTab(progress_tab, "Tiến trình theo Shop")

        # Tab 2: Log chi tiết
        log_tab = QWidget()
        log_layout = QVBoxLayout(log_tab)

        # Thêm log text
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)

        tab_widget.addTab(log_tab, "Log chi tiết")

        # Thêm tab widget vào layout chính
        layout.addWidget(tab_widget)

        # Nút quay lại
        btn_layout = QHBoxLayout()
        back_btn = QPushButton("Quay lại")
        back_btn.clicked.connect(self.go_back_to_step1)
        btn_layout.addWidget(back_btn)
        layout.addLayout(btn_layout)

        # Thêm widget vào stacked widget
        self.stacked_widget.addWidget(step2_widget)

    def eventFilter(self, obj, event):
        """Lọc các sự kiện của các đối tượng để xử lý hover cho menu dropdown"""
        if (obj == self.context_button or obj == self.context_button_step2) and event.type() == QEvent.Type.Enter:
            obj.showMenu()
        return super().eventFilter(obj, event)

    def browse_folder(self):
        """Mở hộp thoại chọn thư mục để lưu file"""
        folder = QFileDialog.getExistingDirectory(self, "Chọn thư mục lưu file Excel")
        if folder:
            self.output_path_edit.setText(folder)

    def paste_from_clipboard(self):
        """Dán dữ liệu từ clipboard vào ô nhập ID"""
        clipboard = QApplication.clipboard()
        text = clipboard.text()
        self.id_edit.processAndFormatIds(text)

    def start_processing(self):
        """Bắt đầu quá trình xử lý dữ liệu"""
        # Lấy danh sách ID từ text có định dạng "1. 123456789"
        text = self.id_edit.toPlainText().strip()
        if not text:
            QMessageBox.warning(self, "Cảnh báo", "Vui lòng nhập ít nhất một ID shop!")
            return

        # Lọc lấy chỉ ID số, bỏ qua số thứ tự
        shop_ids = []
        for line in text.split('\n'):
            if line.strip():
                # Tìm và lấy phần số ID từ dòng (loại bỏ số thứ tự nếu có)
                match = re.search(r'\d+\.\s*(\d+)', line)
                if match:
                    shop_ids.append(match.group(1))
                else:
                    # Nếu không có định dạng số thứ tự, lấy toàn bộ ID số
                    match = re.search(r'(\d+)', line)
                    if match:
                        shop_ids.append(match.group(1))

        if not shop_ids:
            QMessageBox.warning(self, "Cảnh báo", "Không tìm thấy ID shop hợp lệ!")
            return

        # Kiểm tra đường dẫn lưu file
        output_path = self.output_path_edit.text().strip()
        if not output_path:
            QMessageBox.warning(self, "Cảnh báo", "Vui lòng chọn nơi lưu file!")
            return

        # Kiểm tra tên file
        output_filename = self.filename_edit.text().strip()
        if not output_filename:
            QMessageBox.warning(self, "Cảnh báo", "Vui lòng nhập tên file!")
            return

        # Chuyển sang bước 2
        self.stacked_widget.setCurrentIndex(1)

        # Xóa log cũ và bảng tiến trình
        self.log_text.clear()
        self.progress_model.clearData()
        self.total_products_count = 0
        self.total_products_label.setText("📊 Tổng số sản phẩm: 0")

        # Khởi tạo bảng tiến trình với dữ liệu shop IDs
        for shop_id in shop_ids:
            self.progress_model.addShop(shop_id, STATUS_WAITING)

        # Cập nhật progress bar và label tiến trình
        self.progress_bar.setValue(0)
        self.shop_counter_label.setText(f"🔄 Hoàn thành: 0/{len(shop_ids)} shop")

        # Bắt đầu timer
        self.start_time = time.time()
        self.timer.start(1000)  # Cập nhật mỗi giây

        # Tạo và chạy worker thread
        self.worker_thread = WorkerThread(
            self.username,
            self.password,
            shop_ids,
            output_path,
            output_filename
        )
        self.worker_thread.update_log.connect(self.update_log)
        self.worker_thread.update_shop_status.connect(self.update_shop_status)
        self.worker_thread.update_completed.connect(self.update_completed_progress)
        self.worker_thread.update_total_products.connect(self.update_total_products)
        self.worker_thread.finished.connect(self.processing_finished)

        # Cập nhật ScrapingStateManager
        self.scraping_state.start_scraping(
            self.worker_thread,
            f"Data Scraping ({len(shop_ids)} shops)",
            len(shop_ids)
        )

        # Kết nối signal để cập nhật progress trong state manager
        self.worker_thread.update_completed.connect(
            lambda completed, total: self.scraping_state.update_progress(completed)
        )

        self.worker_thread.start()

    def update_log(self, message):
        """Cập nhật log xử lý"""
        self.log_text.append(message)
        # Tự động cuộn xuống
        self.log_text.verticalScrollBar().setValue(self.log_text.verticalScrollBar().maximum())

    def update_shop_status(self, shop_id, status, product_count):
        """Cập nhật trạng thái xử lý của một shop cụ thể"""
        self.progress_model.updateShopStatus(shop_id, status, product_count)

    def update_completed_progress(self, completed, total):
        """Cập nhật tiến trình dựa trên số shop đã hoàn thành"""
        if total > 0:
            percent = int(completed * 100 / total)
            self.progress_bar.setValue(percent)
            self.percent_label.setText(f"{percent}%")
        else:
            self.progress_bar.setValue(0)
            self.percent_label.setText("0%")
        self.shop_counter_label.setText(f"🔄 Hoàn thành: {completed}/{total} shop")

    def update_total_products(self, total):
        """Cập nhật tổng số sản phẩm đã thu thập"""
        self.total_products_count = total
        self.total_products_label.setText(f"📊 Tổng số sản phẩm: {total:,}".replace(',', '.'))

    def update_timer(self):
        """Cập nhật thời gian xử lý"""
        if self.start_time:
            elapsed_time = time.time() - self.start_time
            hours, remainder = divmod(elapsed_time, 3600)
            minutes, seconds = divmod(remainder, 60)
            self.timer_label.setText(f"⏱️ Thời gian: {int(hours):02d}:{int(minutes):02d}:{int(seconds):02d}")

    def processing_finished(self, success, result):
        """Xử lý khi quá trình hoàn tất"""
        self.timer.stop()

        # Cập nhật state manager
        if success:
            self.scraping_state.set_state(ScrapingState.COMPLETED)
            QMessageBox.information(self, "Thành công", f"Đã lưu {self.total_products_count:,} sản phẩm vào file:\n{result}".replace(',', '.'))
        else:
            self.scraping_state.set_state(ScrapingState.ERROR)
            QMessageBox.critical(self, "Lỗi", f"Xử lý dữ liệu thất bại: {result}")

        # Reset state manager sau một khoảng thời gian ngắn
        QTimer.singleShot(1000, self.scraping_state.reset)

    def go_back_to_step1(self):
        """Quay lại bước 1"""
        self.stacked_widget.setCurrentIndex(0)

    def load_login_info(self):
        """Tải thông tin đăng nhập từ file"""
        try:
            if os.path.exists(LOGIN_INFO_FILE):
                with open(LOGIN_INFO_FILE, 'r') as f:
                    lines = f.readlines()
                    if len(lines) >= 2:
                        global DEFAULT_USERNAME, DEFAULT_PASSWORD
                        DEFAULT_USERNAME = lines[0].strip()
                        DEFAULT_PASSWORD = lines[1].strip()
        except Exception as e:
            print(f"Lỗi khi đọc thông tin đăng nhập: {e}")

    def save_login_info(self, username, password):
        """Lưu thông tin đăng nhập vào file"""
        try:
            with open(LOGIN_INFO_FILE, 'w') as f:
                f.write(f"{username}\n{password}")
        except Exception as e:
            print(f"Lỗi khi lưu thông tin đăng nhập: {e}")

    def manual_convert_csv(self):
        """Cho phép user chọn CSV file và convert thủ công"""
        csv_file, _ = QFileDialog.getOpenFileName(
            self, "Chọn file CSV để convert", "", "CSV files (*.csv)"
        )

        if not csv_file:
            return

        if not os.path.exists(csv_file):
            QMessageBox.warning(self, "Lỗi", "File CSV không tồn tại!")
            return

        # Tạo tên file Excel
        excel_file = csv_file.replace('.csv', '.xlsx')
        if excel_file == csv_file:  # Nếu không có .csv extension
            excel_file = csv_file + '.xlsx'

        # Hỏi user có muốn chọn nơi lưu Excel không
        reply = QMessageBox.question(
            self, "Xác nhận",
            f"Convert CSV thành Excel?\n\nFile CSV: {os.path.basename(csv_file)}\nFile Excel: {os.path.basename(excel_file)}",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            # Tạo và chạy conversion thread
            self.csv_thread = CSVConversionThread(csv_file, excel_file)
            self.csv_thread.conversion_completed.connect(self.on_conversion_completed)
            self.csv_thread.conversion_progress.connect(self.show_conversion_progress)

            # Cập nhật state manager cho CSV conversion
            self.scraping_state.start_csv_conversion(self.csv_thread)

            self.csv_thread.start()

    def on_conversion_completed(self, success, excel_path, error_msg):
        """Xử lý khi conversion hoàn tất"""
        if success:
            QMessageBox.information(
                self, "Thành công",
                f"Đã convert thành công!\n\nFile Excel: {excel_path}"
            )
        else:
            QMessageBox.critical(
                self, "Lỗi",
                f"Conversion thất bại!\n\nLỗi: {error_msg}"
            )

    def show_conversion_progress(self, message):
        """Hiển thị tiến trình conversion"""
        # Có thể thêm progress dialog sau này
        print(f"Conversion: {message}")

    def show_login_dialog(self):
        """Hiển thị dialog chỉnh sửa thông tin đăng nhập"""
        global DEFAULT_USERNAME, DEFAULT_PASSWORD

        dialog = LoginDialog(DEFAULT_USERNAME, DEFAULT_PASSWORD, self)

        if dialog.exec() == QDialog.DialogCode.Accepted:
            # Lấy thông tin mới
            new_username, new_password = dialog.get_credentials()

            # Cập nhật biến global
            DEFAULT_USERNAME = new_username
            DEFAULT_PASSWORD = new_password

            # Lưu vào file
            self.save_login_info(new_username, new_password)

            # Thông báo thành công
            QMessageBox.information(
                self, "Thành công",
                "Đã cập nhật thông tin đăng nhập!\nThông tin mới sẽ được sử dụng cho lần xử lý tiếp theo."
            )

            # Force refresh authentication với thông tin mới
            try:
                core.get_logged_in_cookies(new_username, new_password, headless=True, force_refresh=True)
            except Exception as e:
                QMessageBox.warning(
                    self, "Cảnh báo",
                    f"Không thể đăng nhập với thông tin mới:\n{str(e)}\n\nVui lòng kiểm tra lại thông tin đăng nhập."
                )

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec())
