#!/usr/bin/env python3
"""
Test script để kiểm tra ScrapingStateManager và các tính năng mới
"""

import sys
import os
import time
from PyQt6.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QLabel
from PyQt6.QtCore import QTimer

# Thêm đường dẫn để import
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data_scrape_ui import ScrapingStateManager, ScrapingState, ConfirmationDialog, ScrapingMessages

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Test Scraping State Manager")
        self.setGeometry(100, 100, 400, 300)
        
        # Khởi tạo state manager
        self.scraping_state = ScrapingStateManager()
        
        # Setup UI
        self.setup_ui()
        
        # Timer để cập nhật thông tin
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_status)
        self.timer.start(1000)  # Cập nhật mỗi giây
        
    def setup_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Status label
        self.status_label = QLabel("State: IDLE")
        layout.addWidget(self.status_label)
        
        # Duration label
        self.duration_label = QLabel("Duration: 0:00")
        layout.addWidget(self.duration_label)
        
        # Progress label
        self.progress_label = QLabel("Progress: 0%")
        layout.addWidget(self.progress_label)
        
        # Buttons
        start_btn = QPushButton("Start Mock Scraping")
        start_btn.clicked.connect(self.start_mock_scraping)
        layout.addWidget(start_btn)
        
        stop_btn = QPushButton("Stop Scraping")
        stop_btn.clicked.connect(self.stop_scraping)
        layout.addWidget(stop_btn)
        
        confirm_btn = QPushButton("Test Confirmation Dialog")
        confirm_btn.clicked.connect(self.test_confirmation)
        layout.addWidget(confirm_btn)
        
        reset_btn = QPushButton("Reset State")
        reset_btn.clicked.connect(self.reset_state)
        layout.addWidget(reset_btn)
        
    def start_mock_scraping(self):
        """Bắt đầu mock scraping process"""
        # Tạo mock worker thread (None vì chỉ test)
        self.scraping_state.start_scraping(
            None,  # Mock thread
            "Test Scraping Process",
            10  # 10 shops
        )
        
        # Simulate progress
        self.progress_timer = QTimer()
        self.progress_timer.timeout.connect(self.simulate_progress)
        self.progress_timer.start(2000)  # Cập nhật progress mỗi 2 giây
        self.current_progress = 0
        
    def simulate_progress(self):
        """Simulate scraping progress"""
        self.current_progress += 1
        self.scraping_state.update_progress(self.current_progress)
        
        if self.current_progress >= 10:
            self.progress_timer.stop()
            self.scraping_state.set_state(ScrapingState.COMPLETED)
            
    def stop_scraping(self):
        """Dừng scraping process"""
        if hasattr(self, 'progress_timer'):
            self.progress_timer.stop()
        self.scraping_state.set_state(ScrapingState.STOPPING)
        
        # Simulate stop delay
        QTimer.singleShot(1000, lambda: self.scraping_state.set_state(ScrapingState.COMPLETED))
        
    def test_confirmation(self):
        """Test confirmation dialog"""
        if self.scraping_state.is_running():
            dialog = ConfirmationDialog(self.scraping_state, self)
            result = dialog.exec()
            
            if result == dialog.DialogCode.Accepted:
                print("User chose to continue")
            else:
                print("User chose to stop")
        else:
            print("No active scraping process")
            
    def reset_state(self):
        """Reset state manager"""
        if hasattr(self, 'progress_timer'):
            self.progress_timer.stop()
        self.scraping_state.reset()
        self.current_progress = 0
        
    def update_status(self):
        """Cập nhật status display"""
        self.status_label.setText(f"State: {self.scraping_state.state.value}")
        self.duration_label.setText(f"Duration: {self.scraping_state.get_duration()}")
        self.progress_label.setText(f"Progress: {self.scraping_state.get_progress_text()}")

def main():
    app = QApplication(sys.argv)
    window = TestWindow()
    window.show()
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
