#!/usr/bin/env python3
"""
Test script để kiểm tra ScrapingStateManager và các tính năng mới
"""

import sys
import os
import time
from PyQt6.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QLabel
from PyQt6.QtCore import QTimer

# Thêm đường dẫn để import
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data_scrape_ui import ScrapingStateManager, ScrapingState, ConfirmationDialog, ScrapingMessages

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Test Scraping State Manager")
        self.setGeometry(100, 100, 400, 300)

        # Khởi tạo state manager
        self.scraping_state = ScrapingStateManager()

        # Setup UI
        self.setup_ui()

        # Timer để cập nhật thông tin
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_status)
        self.timer.start(1000)  # Cập nhật mỗi giây

    def setup_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)

        # Status label
        self.status_label = QLabel("State: IDLE")
        layout.addWidget(self.status_label)

        # Duration label
        self.duration_label = QLabel("Duration: 0:00")
        layout.addWidget(self.duration_label)

        # Progress label
        self.progress_label = QLabel("Progress: 0%")
        layout.addWidget(self.progress_label)

        # Buttons
        start_btn = QPushButton("Start Mock Scraping")
        start_btn.clicked.connect(self.start_mock_scraping)
        layout.addWidget(start_btn)

        stop_btn = QPushButton("Stop Scraping")
        stop_btn.clicked.connect(self.stop_scraping)
        layout.addWidget(stop_btn)

        confirm_btn = QPushButton("Test Confirmation Dialog")
        confirm_btn.clicked.connect(self.test_confirmation)
        layout.addWidget(confirm_btn)

        reset_btn = QPushButton("Reset State")
        reset_btn.clicked.connect(self.reset_state)
        layout.addWidget(reset_btn)

        step2_btn = QPushButton("Simulate Step 2 Active")
        step2_btn.clicked.connect(self.simulate_step2_active)
        layout.addWidget(step2_btn)

        restore_btn = QPushButton("Test Restore UI State")
        restore_btn.clicked.connect(self.test_restore_ui)
        layout.addWidget(restore_btn)

        ui_state_btn = QPushButton("Test UI State Persistence")
        ui_state_btn.clicked.connect(self.test_ui_state_persistence)
        layout.addWidget(ui_state_btn)

    def start_mock_scraping(self):
        """Bắt đầu mock scraping process"""
        # Tạo mock worker thread (None vì chỉ test)
        self.scraping_state.start_scraping(
            None,  # Mock thread
            "Test Scraping Process",
            10  # 10 shops
        )

        # Simulate progress
        self.progress_timer = QTimer()
        self.progress_timer.timeout.connect(self.simulate_progress)
        self.progress_timer.start(2000)  # Cập nhật progress mỗi 2 giây
        self.current_progress = 0

    def simulate_progress(self):
        """Simulate scraping progress"""
        self.current_progress += 1
        self.scraping_state.update_progress(self.current_progress)

        if self.current_progress >= 10:
            self.progress_timer.stop()
            self.scraping_state.set_state(ScrapingState.COMPLETED)

    def stop_scraping(self):
        """Dừng scraping process"""
        if hasattr(self, 'progress_timer'):
            self.progress_timer.stop()
        self.scraping_state.set_state(ScrapingState.STOPPING)

        # Simulate stop delay
        QTimer.singleShot(1000, lambda: self.scraping_state.set_state(ScrapingState.COMPLETED))

    def test_confirmation(self):
        """Test confirmation dialog"""
        if self.scraping_state.is_running():
            dialog = ConfirmationDialog(self.scraping_state, self)
            result = dialog.exec()

            if result == dialog.DialogCode.Accepted:
                print("User chose to continue")
            else:
                print("User chose to stop")
        else:
            print("No active scraping process")

    def reset_state(self):
        """Reset state manager"""
        if hasattr(self, 'progress_timer'):
            self.progress_timer.stop()
        self.scraping_state.reset()
        self.current_progress = 0

    def simulate_step2_active(self):
        """Simulate có tiến trình đang chạy ở step 2"""
        self.scraping_state.start_scraping(
            None,  # Mock thread
            "Test Background Process",
            5  # 5 shops
        )
        self.scraping_state.set_current_step(1)  # Set to step 2
        self.scraping_state.update_progress(2)  # 2/5 completed
        print("Simulated active process at step 2")

    def test_restore_ui(self):
        """Test restore UI state logic"""
        should_restore = self.scraping_state.should_restore_to_step2()
        current_step = self.scraping_state.get_current_step()
        is_active = self.scraping_state.is_active()

        print(f"Should restore to step 2: {should_restore}")
        print(f"Current step: {current_step}")
        print(f"Is active: {is_active}")
        print(f"Process name: {self.scraping_state.process_name}")
        print(f"Progress: {self.scraping_state.get_progress_text()}")
        print(f"Duration: {self.scraping_state.get_duration()}")

    def test_ui_state_persistence(self):
        """Test UI state persistence functionality"""
        # Simulate saving UI state
        shop_ids = ["shop1", "shop2", "shop3"]
        self.scraping_state.save_ui_state(shop_ids, "/test/path", "test.csv")

        # Simulate updating UI state
        self.scraping_state.update_ui_state(
            total_products=150,
            shop_status_data={
                "shop1": {"status": "✅ Hoàn thành", "product_count": 50},
                "shop2": {"status": "🔄 Đang xử lý", "product_count": 0},
                "shop3": {"status": "⏳ Chờ xử lý", "product_count": 0}
            },
            log_messages=[
                "🚀 Bắt đầu xử lý shop1",
                "✅ Hoàn thành shop1 với 50 sản phẩm",
                "🚀 Bắt đầu xử lý shop2"
            ]
        )

        # Get and display UI state
        ui_state = self.scraping_state.get_ui_state()
        print("=== UI State Persistence Test ===")
        print(f"Shop IDs: {ui_state['shop_ids']}")
        print(f"Output path: {ui_state['output_path']}")
        print(f"Total products: {ui_state['total_products']}")
        print(f"Shop status data: {ui_state['shop_status_data']}")
        print(f"Log messages count: {len(ui_state['log_messages'])}")
        print("Log messages:")
        for msg in ui_state['log_messages']:
            print(f"  - {msg}")
        print("=== End Test ===")

        # Test should_restore_to_step2
        self.scraping_state.set_current_step(1)
        self.scraping_state.set_state(ScrapingState.RUNNING)
        print(f"Should restore to step2: {self.scraping_state.should_restore_to_step2()}")

    def update_status(self):
        """Cập nhật status display"""
        self.status_label.setText(f"State: {self.scraping_state.state.value}")
        self.duration_label.setText(f"Duration: {self.scraping_state.get_duration()}")
        self.progress_label.setText(f"Progress: {self.scraping_state.get_progress_text()}")

def main():
    app = QApplication(sys.argv)
    window = TestWindow()
    window.show()
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
